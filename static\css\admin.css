/* Fresh Admin Interface - Professional Green Theme */

/* Enhanced Dashboard Cards with Fresh Theme */
.admin-dashboard-card {
    border-left: 5px solid;
    border-radius: var(--border-radius-lg);
    transition: all 0.3s ease;
    background: var(--pure-white);
    box-shadow: var(--fresh-shadow);
    border: 2px solid var(--fresh-mint);
    position: relative;
    overflow: hidden;
}

.admin-dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--nature-gradient);
}

.admin-dashboard-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--organic-shadow);
    border-color: var(--fresh-primary);
}

.admin-dashboard-card.primary {
    border-left-color: var(--fresh-primary);
}

.admin-dashboard-card.success {
    border-left-color: var(--fresh-secondary);
}

.admin-dashboard-card.warning {
    border-left-color: var(--sunshine-yellow);
}

.admin-dashboard-card.danger {
    border-left-color: #dc3545;
}

.admin-dashboard-card .card-icon {
    font-size: 3rem;
    opacity: 0.9;
    color: var(--fresh-primary);
    transition: all 0.3s ease;
}

.admin-dashboard-card:hover .card-icon {
    transform: scale(1.1);
    opacity: 1;
}

/* Fresh Admin Table Styling */
.table {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--fresh-shadow);
    border: 2px solid var(--fresh-mint);
}

.table th {
    background: var(--fresh-gradient);
    color: var(--text-on-green);
    font-weight: 600;
    border: none;
    padding: 1rem;
}

.table td {
    padding: 0.75rem 1rem;
    border-color: var(--fresh-mint);
    vertical-align: middle;
}

.table tbody tr:hover {
    background: var(--leaf-gradient);
    transform: scale(1.01);
    transition: all 0.2s ease;
}

/* Enhanced Fresh Status Badges */
.badge {
    font-size: 0.85rem;
    padding: 8px 16px;
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    box-shadow: var(--fresh-shadow);
}

/* Form controls */
.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Admin navbar customization */
.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: #fff;
}

/* Fresh Admin Sidebar (for larger screens) */
@media (min-width: 992px) {
    .admin-sidebar {
        min-height: calc(100vh - 56px);
        background: var(--fresh-gradient);
        padding-top: 1rem;
        border-radius: 0 var(--border-radius-lg) var(--border-radius-lg) 0;
        box-shadow: var(--fresh-shadow);
    }

    .admin-sidebar .nav-link {
        color: rgba(255, 255, 255, 0.9);
        padding: 1rem 1.5rem;
        border-left: 4px solid transparent;
        margin: 0.25rem 0;
        border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .admin-sidebar .nav-link:hover {
        color: #fff;
        background: rgba(255, 255, 255, 0.15);
        border-left-color: var(--sunshine-yellow);
        transform: translateX(5px);
    }

    .admin-sidebar .nav-link.active {
        color: #fff;
        background: rgba(255, 255, 255, 0.2);
        border-left-color: var(--sunshine-yellow);
        box-shadow: inset 0 0 10px rgba(0,0,0,0.1);
    }

    .admin-sidebar .nav-link i {
        margin-right: 0.75rem;
        font-size: 1.1rem;
    }
}

/* Fresh Order Status Colors */
.status-pending {
    color: var(--sunshine-yellow);
    background: linear-gradient(135deg, var(--sunshine-yellow), #f57c00);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.status-processing {
    color: var(--sky-blue);
    background: linear-gradient(135deg, var(--sky-blue), #0288d1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.status-shipped {
    color: var(--fresh-primary);
    background: var(--nature-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.status-delivered {
    color: var(--fresh-secondary);
    background: var(--fresh-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.status-cancelled {
    color: #dc3545;
    background: linear-gradient(135deg, #dc3545, #b71c1c);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

/* Fresh Admin Form Enhancements */
.admin-form-container {
    background: var(--pure-white);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--fresh-shadow);
    border: 2px solid var(--fresh-mint);
}

.admin-form-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--nature-gradient);
}

/* Fresh Admin Buttons */
.btn-admin-primary {
    background: var(--fresh-gradient);
    border: none;
    color: var(--text-on-green);
    border-radius: var(--border-radius-md);
    padding: 12px 24px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: var(--fresh-shadow);
}

.btn-admin-primary:hover {
    background: var(--fresh-primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--fresh-shadow-hover);
    color: var(--text-on-green);
}

.btn-admin-secondary {
    background: var(--leaf-gradient);
    border: 2px solid var(--fresh-secondary);
    color: var(--fresh-primary-dark);
    border-radius: var(--border-radius-md);
    padding: 10px 22px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-admin-secondary:hover {
    background: var(--fresh-secondary);
    color: var(--text-on-green);
    transform: translateY(-2px);
    box-shadow: var(--fresh-shadow);
}

/* Image preview */
.img-preview {
    max-width: 100px;
    max-height: 100px;
    object-fit: contain;
}