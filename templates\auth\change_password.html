{% extends "base.html" %}
{% block title %}Change Password - Fresh Grocery{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <!-- Change Password Card -->
            <div class="auth-card">
                <div class="auth-header">
                    <div class="auth-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <h2 class="auth-title">Change Password</h2>
                    <p class="auth-subtitle">Update your account password</p>
                </div>
                
                <div class="auth-body">
                    <form method="POST" action="{{ url_for('change_password') }}">
                        {{ form.hidden_tag() }}
                        
                        <!-- Current Password -->
                        <div class="form-group mb-3">
                            {{ form.current_password.label(class="form-label auth-label") }}
                            <div class="password-input-wrapper">
                                {{ form.current_password(class="form-control auth-input" + (" is-invalid" if form.current_password.errors else ""), placeholder="Enter your current password") }}
                                <button type="button" class="password-toggle" onclick="togglePassword('current_password')">
                                    <i class="fas fa-eye" id="current_password-eye"></i>
                                </button>
                            </div>
                            {% if form.current_password.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.current_password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- New Password -->
                        <div class="form-group mb-3">
                            {{ form.new_password.label(class="form-label auth-label") }}
                            <div class="password-input-wrapper">
                                {{ form.new_password(class="form-control auth-input" + (" is-invalid" if form.new_password.errors else ""), placeholder="Enter your new password") }}
                                <button type="button" class="password-toggle" onclick="togglePassword('new_password')">
                                    <i class="fas fa-eye" id="new_password-eye"></i>
                                </button>
                            </div>
                            {% if form.new_password.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.new_password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                Password must be at least 6 characters long.
                            </small>
                        </div>
                        
                        <!-- Confirm New Password -->
                        <div class="form-group mb-4">
                            {{ form.new_password2.label(class="form-label auth-label") }}
                            <div class="password-input-wrapper">
                                {{ form.new_password2(class="form-control auth-input" + (" is-invalid" if form.new_password2.errors else ""), placeholder="Confirm your new password") }}
                                <button type="button" class="password-toggle" onclick="togglePassword('new_password2')">
                                    <i class="fas fa-eye" id="new_password2-eye"></i>
                                </button>
                            </div>
                            {% if form.new_password2.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.new_password2.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Password Strength Indicator -->
                        <div class="password-strength mb-4">
                            <div class="strength-label">Password Strength:</div>
                            <div class="strength-bar">
                                <div class="strength-fill" id="strength-fill"></div>
                            </div>
                            <div class="strength-text" id="strength-text">Enter a password</div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="form-group mb-3">
                            <div class="row">
                                <div class="col-md-6">
                                    {{ form.submit(class="btn auth-btn-primary w-100") }}
                                </div>
                                <div class="col-md-6">
                                    <a href="{{ url_for('profile') }}" class="btn auth-btn-secondary w-100">
                                        <i class="fas fa-times"></i> Cancel
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                    
                    <!-- Security Tips -->
                    <div class="security-tips">
                        <h6 class="tips-title">
                            <i class="fas fa-shield-alt"></i> Password Security Tips
                        </h6>
                        <ul class="tips-list">
                            <li>Use at least 8 characters</li>
                            <li>Include uppercase and lowercase letters</li>
                            <li>Add numbers and special characters</li>
                            <li>Avoid common words or personal information</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Authentication Styles */
.auth-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(46, 125, 50, 0.15);
    border: 2px solid #e8f5e8;
    overflow: hidden;
}

.auth-header {
    background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
    padding: 2rem;
    text-align: center;
    border-bottom: 1px solid #e8f5e8;
}

.auth-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    box-shadow: 0 8px 25px rgba(46, 125, 50, 0.3);
}

.auth-icon i {
    font-size: 2rem;
    color: white;
}

.auth-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: #1B5E20;
    margin-bottom: 0.5rem;
}

.auth-subtitle {
    color: #2E7D32;
    font-size: 1rem;
    margin: 0;
}

.auth-body {
    padding: 2rem;
}

.auth-label {
    color: #1B5E20;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.auth-input {
    border: 2px solid #e8f5e8;
    border-radius: 10px;
    padding: 12px 16px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.auth-input:focus {
    border-color: #4CAF50;
    box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
}

.password-input-wrapper {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 4px;
}

.password-toggle:hover {
    color: #2E7D32;
}

.password-strength {
    margin-bottom: 1rem;
}

.strength-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #2E7D32;
    margin-bottom: 0.5rem;
}

.strength-bar {
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.strength-fill {
    height: 100%;
    width: 0%;
    transition: all 0.3s ease;
    border-radius: 4px;
}

.strength-text {
    font-size: 0.8rem;
    font-weight: 500;
}

.auth-btn-primary {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    border: none;
    border-radius: 10px;
    padding: 12px 24px;
    font-weight: 600;
    font-size: 1rem;
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(46, 125, 50, 0.3);
}

.auth-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(46, 125, 50, 0.4);
    color: white;
}

.auth-btn-secondary {
    background: transparent;
    border: 2px solid #4CAF50;
    border-radius: 10px;
    padding: 10px 24px;
    font-weight: 600;
    color: #2E7D32;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.auth-btn-secondary:hover {
    background: #4CAF50;
    color: white;
    transform: translateY(-2px);
    text-decoration: none;
}

.security-tips {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 2rem;
    border: 1px solid #e9ecef;
}

.tips-title {
    color: #2E7D32;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.tips-list {
    margin: 0;
    padding-left: 1.5rem;
}

.tips-list li {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}
</style>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const eye = document.getElementById(fieldId + '-eye');
    
    if (field.type === 'password') {
        field.type = 'text';
        eye.classList.remove('fa-eye');
        eye.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        eye.classList.remove('fa-eye-slash');
        eye.classList.add('fa-eye');
    }
}

// Password strength checker
document.getElementById('new_password').addEventListener('input', function() {
    const password = this.value;
    const strengthFill = document.getElementById('strength-fill');
    const strengthText = document.getElementById('strength-text');
    
    let strength = 0;
    let text = '';
    let color = '';
    
    if (password.length >= 6) strength += 1;
    if (password.length >= 8) strength += 1;
    if (/[a-z]/.test(password) && /[A-Z]/.test(password)) strength += 1;
    if (/\d/.test(password)) strength += 1;
    if (/[^a-zA-Z\d]/.test(password)) strength += 1;
    
    switch (strength) {
        case 0:
        case 1:
            text = 'Very Weak';
            color = '#dc3545';
            break;
        case 2:
            text = 'Weak';
            color = '#fd7e14';
            break;
        case 3:
            text = 'Fair';
            color = '#ffc107';
            break;
        case 4:
            text = 'Good';
            color = '#20c997';
            break;
        case 5:
            text = 'Strong';
            color = '#28a745';
            break;
    }
    
    strengthFill.style.width = (strength * 20) + '%';
    strengthFill.style.background = color;
    strengthText.textContent = text;
    strengthText.style.color = color;
});
</script>
{% endblock %}
