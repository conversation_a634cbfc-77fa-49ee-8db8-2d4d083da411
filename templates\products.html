{% extends "base.html" %}

{% block extra_css %}
<style>
    .product-filters {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .product-card {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        margin-bottom: 2rem;
    }
    
    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .product-image {
        height: 200px;
        overflow: hidden;
        position: relative;
    }
    
    .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }
    
    .product-card:hover .product-image img {
        transform: scale(1.1);
    }
    
    .product-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        background: #28a745;
        color: white;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    
    .product-details {
        padding: 1.5rem;
    }
    
    .product-category {
        color: #6c757d;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 0.5rem;
    }
    
    .product-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }
    
    .product-price {
        font-size: 1.4rem;
        font-weight: bold;
        color: #28a745;
        margin-bottom: 1rem;
    }
    
    .product-description {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }
    
    .btn-add-cart {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        color: white;
        padding: 10px 20px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        width: 100%;
    }
    
    .btn-add-cart:hover {
        background: linear-gradient(135deg, #20c997 0%, #28a745 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }
    
    .btn-out-of-stock {
        background: #6c757d;
        color: white;
        padding: 10px 20px;
        border-radius: 25px;
        font-weight: 600;
        width: 100%;
        border: none;
    }
    
    .pagination-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 3rem;
    }
    
    .filter-form {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <!-- Filters Sidebar -->
        <div class="col-lg-3 mb-4">
            <div class="filter-form">
                <h5 class="mb-3">Filter Products</h5>
                <form method="GET" action="{{ url_for('products') }}">
                    <!-- Search -->
                    <div class="mb-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ form.search.data or '' }}" placeholder="Search products...">
                    </div>
                    
                    <!-- Category -->
                    <div class="mb-3">
                        <label for="category_id" class="form-label">Category</label>
                        <select class="form-select" id="category_id" name="category_id">
                            <option value="">All Categories</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}" 
                                    {% if form.category_id.data == category.id %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <!-- Price Range -->
                    <div class="row mb-3">
                        <div class="col-6">
                            <label for="min_price" class="form-label">Min Price</label>
                            <input type="number" class="form-control" id="min_price" name="min_price" 
                                   value="{{ form.min_price.data or '' }}" min="0" step="0.01" placeholder="$0.00">
                        </div>
                        <div class="col-6">
                            <label for="max_price" class="form-label">Max Price</label>
                            <input type="number" class="form-control" id="max_price" name="max_price" 
                                   value="{{ form.max_price.data or '' }}" min="0" step="0.01" placeholder="$999.99">
                        </div>
                    </div>
                    
                    <!-- Filters -->
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="in_stock_only" name="in_stock_only" value="y"
                                   {% if form.in_stock_only.data %}checked{% endif %}>
                            <label class="form-check-label" for="in_stock_only">
                                In Stock Only
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="featured_only" name="featured_only" value="y"
                                   {% if form.featured_only.data %}checked{% endif %}>
                            <label class="form-check-label" for="featured_only">
                                Featured Only
                            </label>
                        </div>
                    </div>
                    
                    <!-- Sort -->
                    <div class="mb-3">
                        <label for="sort_by" class="form-label">Sort By</label>
                        <select class="form-select" id="sort_by" name="sort_by">
                            <option value="name_asc" {% if form.sort_by.data == 'name_asc' %}selected{% endif %}>Name (A-Z)</option>
                            <option value="name_desc" {% if form.sort_by.data == 'name_desc' %}selected{% endif %}>Name (Z-A)</option>
                            <option value="price_asc" {% if form.sort_by.data == 'price_asc' %}selected{% endif %}>Price (Low to High)</option>
                            <option value="price_desc" {% if form.sort_by.data == 'price_desc' %}selected{% endif %}>Price (High to Low)</option>
                            <option value="created_desc" {% if form.sort_by.data == 'created_desc' %}selected{% endif %}>Newest First</option>
                            <option value="created_asc" {% if form.sort_by.data == 'created_asc' %}selected{% endif %}>Oldest First</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-fresh-primary w-100">Apply Filters</button>
                    <a href="{{ url_for('products') }}" class="btn btn-outline-secondary w-100 mt-2">Clear Filters</a>
                </form>
            </div>
        </div>
        
        <!-- Products Grid -->
        <div class="col-lg-9">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Products</h2>
                <span class="text-muted">{{ products.total }} products found</span>
            </div>
            
            {% if products.items %}
                <div class="row">
                    {% for product in products.items %}
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="product-card">
                            {% if product.is_featured %}
                            <div class="product-badge">Featured</div>
                            {% endif %}
                            
                            <div class="product-image">
                                <img src="{{ product.get_main_image() }}" alt="{{ product.name }}">
                            </div>
                            
                            <div class="product-details">
                                <div class="product-category">{{ product.category.name if product.category else 'Uncategorized' }}</div>
                                <h5 class="product-title">{{ product.name }}</h5>
                                <div class="product-price">${{ "%.2f"|format(product.price) }}</div>
                                
                                {% if product.short_description %}
                                <div class="product-description">{{ product.short_description|truncate(80) }}</div>
                                {% elif product.description %}
                                <div class="product-description">{{ product.description|truncate(80) }}</div>
                                {% endif %}
                                
                                {% if product.is_in_stock() %}
                                <form action="{{ url_for('add_to_cart', item_name=product.name) }}" method="POST" class="d-flex align-items-center">
                                    <input type="number" name="quantity" value="1" min="1" max="{{ product.stock_quantity }}"
                                           class="form-control me-2" style="width: 80px;">
                                    <button type="submit" class="btn-add-cart flex-grow-1">
                                        <i class="fas fa-cart-plus"></i> Add to Cart
                                    </button>
                                </form>
                                {% else %}
                                <button class="btn-out-of-stock" disabled>
                                    <i class="fas fa-times"></i> Out of Stock
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- Pagination -->
                {% if products.pages > 1 %}
                <div class="pagination-wrapper">
                    <nav aria-label="Product pagination">
                        <ul class="pagination">
                            {% if products.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('products', page=products.prev_num, **request.args) }}">Previous</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in products.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != products.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('products', page=page_num, **request.args) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if products.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('products', page=products.next_num, **request.args) }}">Next</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h3>No Products Found</h3>
                    <p class="text-muted">We couldn't find any products matching your criteria. Try adjusting your filters.</p>
                    <a href="{{ url_for('products') }}" class="btn btn-fresh-primary">Browse All Products</a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
