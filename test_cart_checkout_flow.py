#!/usr/bin/env python3

"""
Test the complete cart to checkout flow to identify the issue
"""

import sys
import os
import json

def load_grocery_data():
    """Load grocery data from JSON file"""
    try:
        if os.path.exists('grocery_data.json'):
            with open('grocery_data.json', 'r') as f:
                return json.load(f)
        return []
    except Exception as e:
        print(f"Error loading grocery data: {e}")
        return []

def simulate_cart_to_checkout():
    """Simulate the cart to checkout process"""
    print("🧪 Testing Cart to Checkout Flow...")
    
    # Simulate a cart with items (like what add_to_cart creates)
    sample_cart = [
        {
            'name': 'Organic Bananas',
            'price': 2.99,
            'quantity': 2,
            'image': 'banana.jpg'
        },
        {
            'name': 'Apple',
            'price': 0.99,
            'quantity': 3,
            'image': 'apple.jpg'
        }
    ]
    
    print(f"📦 Sample Cart ({len(sample_cart)} items):")
    for item in sample_cart:
        print(f"   - {item['name']}: ${item['price']:.2f} x {item['quantity']} = ${item['price'] * item['quantity']:.2f}")
    
    cart_total = sum(item['price'] * item['quantity'] for item in sample_cart)
    print(f"🛒 Cart Total: ${cart_total:.2f}")
    
    # Load grocery data (what checkout process uses)
    grocery_data = load_grocery_data()
    print(f"\n📄 Grocery Data ({len(grocery_data)} items):")
    for item in grocery_data[:5]:  # Show first 5
        print(f"   - {item.get('name', 'Unknown')}: ${item.get('price', 0):.2f}")
    
    # Simulate NEW checkout process (FIXED version)
    print(f"\n🔄 Simulating NEW Checkout Process (FIXED)...")
    items = []
    total = 0

    for cart_item in sample_cart:
        item_name = cart_item.get('name')
        quantity = cart_item.get('quantity', 0)
        price = cart_item.get('price', 0)  # Use price from cart directly

        print(f"   Processing: {item_name}")

        # Add item to order (no need to look up in grocery_data)
        items.append({
            'name': item_name,
            'price': price,
            'quantity': quantity
        })
        total += price * quantity
        print(f"   ✅ Added to order: ${price:.2f} x {quantity} = ${price * quantity:.2f}")
    
    print(f"\n📊 Checkout Results:")
    print(f"   Items processed: {len(items)}")
    print(f"   Checkout total: ${total:.2f}")
    print(f"   Cart total: ${cart_total:.2f}")
    
    if len(items) == 0:
        print("\n💥 PROBLEM IDENTIFIED:")
        print("   - Cart has items but checkout finds ZERO items")
        print("   - This happens when cart item names don't match grocery_data.json names")
        print("   - Result: Empty 'Items Ordered' table with $0.00 total")
        return False
    elif total != cart_total:
        print(f"\n⚠️ PRICE MISMATCH:")
        print(f"   - Cart total: ${cart_total:.2f}")
        print(f"   - Checkout total: ${total:.2f}")
        print("   - Cart prices and grocery_data prices don't match")
        return False
    else:
        print(f"\n✅ CHECKOUT PROCESS FIXED!")
        print(f"   - All {len(items)} cart items processed successfully")
        print(f"   - Cart total matches checkout total: ${total:.2f}")
        print("   - 'Items Ordered' table will now show all items with correct prices")
        return True

def check_item_name_consistency():
    """Check if cart item names match grocery data names"""
    print(f"\n🔍 Checking Item Name Consistency...")
    
    grocery_data = load_grocery_data()
    grocery_names = [item.get('name') for item in grocery_data]
    
    # Common cart items that might be added
    common_cart_items = ['Organic Bananas', 'Apple', 'Banana', 'Tomato', 'Potato', 'Mango']
    
    print(f"📋 Grocery Data Items:")
    for name in grocery_names[:10]:  # Show first 10
        print(f"   - '{name}'")
    
    print(f"\n🛒 Common Cart Items:")
    for item_name in common_cart_items:
        if item_name in grocery_names:
            print(f"   ✅ '{item_name}' - Found in grocery data")
        else:
            print(f"   ❌ '{item_name}' - NOT found in grocery data")
            # Try to find similar names
            similar = [name for name in grocery_names if item_name.lower() in name.lower() or name.lower() in item_name.lower()]
            if similar:
                print(f"      Similar names: {similar}")

if __name__ == "__main__":
    print("🚀 Cart to Checkout Flow Test")
    print("=" * 50)
    
    success = simulate_cart_to_checkout()
    check_item_name_consistency()
    
    if not success:
        print("\n💥 CART TO CHECKOUT ISSUE IDENTIFIED!")
        print("The checkout process is not finding cart items in grocery_data.json")
        print("This causes empty orders with $0.00 total.")
    else:
        print("\n🎉 Cart to checkout flow is working correctly!")
