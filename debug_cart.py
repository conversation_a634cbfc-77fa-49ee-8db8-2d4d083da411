#!/usr/bin/env python3

"""
Debug script to test cart functionality
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("🔍 Debugging Cart Functionality...")
    
    # Import the app and models
    from app import app, db
    from models import Product, Category
    
    print("✓ Successfully imported app and models")
    
    # Test within app context
    with app.app_context():
        print("✓ App context created")
        
        # Check if database tables exist and have data
        try:
            product_count = Product.query.count()
            category_count = Category.query.count()
            print(f"✓ Database accessible: {product_count} products, {category_count} categories")
        except Exception as e:
            print(f"✗ Database error: {e}")
            sys.exit(1)
        
        # List all products
        products = Product.query.filter_by(is_active=True).all()
        print(f"\n📦 Available Products ({len(products)}):")
        for product in products:
            print(f"  - {product.name} (${product.price}) - Stock: {product.stock_quantity}")
        
        # Test cart session simulation
        print(f"\n🛒 Testing Cart Logic...")
        
        # Simulate adding "Organic Bananas" to cart
        test_item_name = "Organic Bananas"
        product = Product.query.filter_by(name=test_item_name, is_active=True).first()
        
        if product:
            print(f"✓ Found '{test_item_name}' in database")
            print(f"  - Price: ${product.price}")
            print(f"  - Stock: {product.stock_quantity}")
            print(f"  - Image: {product.get_main_image()}")
            
            # Simulate cart item creation
            cart_item = {
                'name': test_item_name,
                'price': float(product.price),
                'quantity': 1,
                'image': product.get_main_image()
            }
            print(f"✓ Cart item would be created: {cart_item}")
        else:
            print(f"✗ '{test_item_name}' not found in database")
            
            # Test JSON fallback
            from app import load_grocery_data
            json_items = load_grocery_data()
            json_item = next((i for i in json_items if i['name'] == test_item_name), None)
            
            if json_item:
                print(f"✓ Found '{test_item_name}' in JSON fallback")
            else:
                print(f"✗ '{test_item_name}' not found in JSON either")
        
        # Test with a JSON item
        test_json_item = "Apple"
        json_product = Product.query.filter_by(name=test_json_item, is_active=True).first()
        
        if not json_product:
            print(f"\n📄 Testing JSON fallback with '{test_json_item}'...")
            from app import load_grocery_data
            json_items = load_grocery_data()
            json_item = next((i for i in json_items if i['name'] == test_json_item), None)
            
            if json_item:
                print(f"✓ Found '{test_json_item}' in JSON: ${json_item['price']}, Stock: {json_item['quantity']}")
                cart_item = {
                    'name': test_json_item,
                    'price': json_item['price'],
                    'quantity': 1,
                    'image': json_item.get('image', 'default.jpg')
                }
                print(f"✓ Cart item would be created: {cart_item}")
            else:
                print(f"✗ '{test_json_item}' not found in JSON")
        
        print(f"\n✅ Cart debugging completed!")
        print(f"The cart functionality should work for both database and JSON products.")

except Exception as e:
    print(f"✗ Error during debugging: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
