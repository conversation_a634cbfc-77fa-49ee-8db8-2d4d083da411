{% extends "base.html" %}
{% block title %}Edit Profile - Fresh Grocery{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <!-- Edit Profile Card -->
            <div class="auth-card">
                <div class="auth-header">
                    <div class="auth-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <h2 class="auth-title">Edit Your Profile</h2>
                    <p class="auth-subtitle">Update your personal information</p>
                </div>
                
                <div class="auth-body">
                    <form method="POST" action="{{ url_for('edit_profile') }}">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <!-- First Name -->
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.first_name.label(class="form-label auth-label") }}
                                    {{ form.first_name(class="form-control auth-input" + (" is-invalid" if form.first_name.errors else "")) }}
                                    {% if form.first_name.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.first_name.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <!-- Last Name -->
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.last_name.label(class="form-label auth-label") }}
                                    {{ form.last_name(class="form-control auth-input" + (" is-invalid" if form.last_name.errors else "")) }}
                                    {% if form.last_name.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.last_name.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Email -->
                        <div class="form-group mb-3">
                            {{ form.email.label(class="form-label auth-label") }}
                            {{ form.email(class="form-control auth-input" + (" is-invalid" if form.email.errors else "")) }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Phone -->
                        <div class="form-group mb-3">
                            {{ form.phone.label(class="form-label auth-label") }}
                            {{ form.phone(class="form-control auth-input" + (" is-invalid" if form.phone.errors else ""), placeholder="Enter your phone number (optional)") }}
                            {% if form.phone.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.phone.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Address -->
                        <div class="form-group mb-3">
                            {{ form.address.label(class="form-label auth-label") }}
                            {{ form.address(class="form-control auth-input" + (" is-invalid" if form.address.errors else ""), rows="3", placeholder="Enter your full address (optional)") }}
                            {% if form.address.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.address.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="row">
                            <!-- City -->
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    {{ form.city.label(class="form-label auth-label") }}
                                    {{ form.city(class="form-control auth-input" + (" is-invalid" if form.city.errors else ""), placeholder="City") }}
                                    {% if form.city.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.city.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <!-- State -->
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    {{ form.state.label(class="form-label auth-label") }}
                                    {{ form.state(class="form-control auth-input" + (" is-invalid" if form.state.errors else ""), placeholder="State") }}
                                    {% if form.state.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.state.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <!-- ZIP Code -->
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    {{ form.zip_code.label(class="form-label auth-label") }}
                                    {{ form.zip_code(class="form-control auth-input" + (" is-invalid" if form.zip_code.errors else ""), placeholder="ZIP Code") }}
                                    {% if form.zip_code.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.zip_code.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="form-group mb-3">
                            <div class="row">
                                <div class="col-md-6">
                                    {{ form.submit(class="btn auth-btn-primary w-100") }}
                                </div>
                                <div class="col-md-6">
                                    <a href="{{ url_for('profile') }}" class="btn auth-btn-secondary w-100">
                                        <i class="fas fa-times"></i> Cancel
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Authentication Styles */
.auth-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(46, 125, 50, 0.15);
    border: 2px solid #e8f5e8;
    overflow: hidden;
}

.auth-header {
    background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
    padding: 2rem;
    text-align: center;
    border-bottom: 1px solid #e8f5e8;
}

.auth-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    box-shadow: 0 8px 25px rgba(46, 125, 50, 0.3);
}

.auth-icon i {
    font-size: 2rem;
    color: white;
}

.auth-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: #1B5E20;
    margin-bottom: 0.5rem;
}

.auth-subtitle {
    color: #2E7D32;
    font-size: 1rem;
    margin: 0;
}

.auth-body {
    padding: 2rem;
}

.auth-label {
    color: #1B5E20;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.auth-input {
    border: 2px solid #e8f5e8;
    border-radius: 10px;
    padding: 12px 16px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.auth-input:focus {
    border-color: #4CAF50;
    box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
}

.auth-btn-primary {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    border: none;
    border-radius: 10px;
    padding: 12px 24px;
    font-weight: 600;
    font-size: 1rem;
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(46, 125, 50, 0.3);
}

.auth-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(46, 125, 50, 0.4);
    color: white;
}

.auth-btn-secondary {
    background: transparent;
    border: 2px solid #4CAF50;
    border-radius: 10px;
    padding: 10px 24px;
    font-weight: 600;
    color: #2E7D32;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.auth-btn-secondary:hover {
    background: #4CAF50;
    color: white;
    transform: translateY(-2px);
    text-decoration: none;
}
</style>
{% endblock %}
