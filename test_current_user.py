#!/usr/bin/env python3

import os
import sys
import json
import requests

def test_current_user():
    """Test what user is currently logged in and their orders"""
    
    # Test if we can access the profile page
    try:
        response = requests.get('http://localhost:5000/profile')
        print(f"Profile page status: {response.status_code}")
        
        if "Login" in response.text:
            print("❌ User is not logged in - redirected to login page")
            return
        
        # Check if we can see user info in the page
        if "<EMAIL>" in response.text:
            print("✅ Logged in as: sksajaru<PERSON><PERSON><EMAIL>")
        elif "<EMAIL>" in response.text:
            print("⚠️ Logged in as: <EMAIL> (wrong user)")
        else:
            print("❓ Unknown user logged in")
            
        # Check for order information
        if "Total Orders" in response.text:
            print("✅ Order statistics section found")
        else:
            print("❌ No order statistics found")
            
        if "Recent Orders" in response.text:
            print("✅ Recent orders section found")
        else:
            print("❌ No recent orders section found")
            
    except Exception as e:
        print(f"❌ Error testing profile page: {e}")

    # Test My Orders page
    try:
        response = requests.get('http://localhost:5000/my-orders')
        print(f"\nMy Orders page status: {response.status_code}")
        
        if "Order History" in response.text:
            print("✅ Order history page loaded")
        else:
            print("❌ Order history page not found")
            
        if "No orders found" in response.text:
            print("⚠️ No orders found message displayed")
        elif "Order #" in response.text:
            print("✅ Orders are displayed")
        else:
            print("❓ Unknown order display status")
            
    except Exception as e:
        print(f"❌ Error testing my-orders page: {e}")

if __name__ == "__main__":
    test_current_user()
