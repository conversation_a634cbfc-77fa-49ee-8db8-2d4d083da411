{% extends "base.html" %}
{% block title %}Home - Fresh Grocery{% endblock %}
{% block content %}
<div class="container py-4">
    <!-- Attractive Hero Section -->
    <div class="attractive-hero-section mb-5">
        <div class="hero-background-pattern"></div>
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="attractive-hero-title">🌿 Welcome to Fresh Grocery</h1>
                <p class="attractive-hero-subtitle">
                    Discover premium quality groceries delivered fresh to your doorstep. From farm-fresh
                    produce to artisan bakery items, we bring you the finest selection for your family.
                </p>
                <div class="attractive-hero-features">
                    <div class="feature-item">
                        <i class="fas fa-truck"></i>
                        <span>Free delivery over $50</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-leaf"></i>
                        <span>100% Fresh guarantee</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-clock"></i>
                        <span>Same day delivery</span>
                    </div>
                </div>
                <a class="attractive-btn-primary attractive-btn-lg" href="{{ url_for('shop') }}" role="button">
                    <i class="fas fa-shopping-cart"></i> Start Shopping
                </a>
            </div>
            <div class="col-lg-4 text-center">
                <div class="attractive-hero-visual">
                    <div class="hero-circle">
                        <i class="fas fa-apple-alt"></i>
                    </div>
                    <div class="floating-icons">
                        <i class="fas fa-carrot floating-icon-1"></i>
                        <i class="fas fa-bread-slice floating-icon-2"></i>
                        <i class="fas fa-cheese floating-icon-3"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Featured Products Section -->
    <section id="featured-products" class="attractive-section mb-5">
        <div class="container">
            <div class="attractive-section-header">
                <div class="section-badge">Featured</div>
                <h2 class="attractive-section-title">Our Best Picks</h2>
                <p class="attractive-section-subtitle">Handpicked premium products just for you</p>
                <div class="section-divider"></div>
            </div>
            
            <div class="row">
                <!-- Display actual featured items if available -->
                {% if featured_items and featured_items|length > 0 %}
                    {% for item in featured_items %}
                    <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-3">
                        <div class="compact-product-card">
                            <div class="compact-badge">⭐</div>
                            <div class="compact-product-image">
                                <img src="{{ item.get_main_image() }}"
                                     alt="{{ item.name }}" class="img-fluid">
                                <div class="compact-overlay">
                                    <i class="fas fa-eye"></i>
                                </div>
                            </div>
                            <div class="compact-product-details">
                                <h5 class="compact-product-title">{{ item.name }}</h5>
                                <div class="compact-price">${{ "%.2f"|format(item.price) }}</div>
                                <form action="{{ url_for('add_to_cart', item_name=item.name) }}" method="POST">
                                    <input type="hidden" name="quantity" value="1">
                                    <button type="submit" class="compact-btn-cart">
                                        <i class="fas fa-cart-plus"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% endif %}

                <!-- Add sample items to show 5 rows (30 items total: 6 items per row) -->
                {% set sample_products = [
                    {'name': 'Fresh Apples', 'price': 3.99, 'badge': '🍎'},
                    {'name': 'Organic Milk', 'price': 4.50, 'badge': '🥛'},
                    {'name': 'Whole Bread', 'price': 2.75, 'badge': '🍞'},
                    {'name': 'Farm Eggs', 'price': 5.25, 'badge': '🥚'},
                    {'name': 'Fresh Carrots', 'price': 2.99, 'badge': '🥕'},
                    {'name': 'Greek Yogurt', 'price': 3.75, 'badge': '🥛'},
                    {'name': 'Bananas', 'price': 1.99, 'badge': '🍌'},
                    {'name': 'Cheddar Cheese', 'price': 6.50, 'badge': '🧀'},
                    {'name': 'Croissants', 'price': 4.25, 'badge': '🥐'},
                    {'name': 'Orange Juice', 'price': 3.50, 'badge': '🍊'},
                    {'name': 'Spinach', 'price': 2.25, 'badge': '🥬'},
                    {'name': 'Butter', 'price': 4.75, 'badge': '🧈'},
                    {'name': 'Strawberries', 'price': 5.99, 'badge': '🍓'},
                    {'name': 'Almond Milk', 'price': 3.25, 'badge': '🥛'},
                    {'name': 'Bagels', 'price': 3.99, 'badge': '🥯'},
                    {'name': 'Tomatoes', 'price': 3.50, 'badge': '🍅'},
                    {'name': 'Broccoli', 'price': 2.75, 'badge': '🥦'},
                    {'name': 'Mozzarella', 'price': 5.50, 'badge': '🧀'},
                    {'name': 'Blueberries', 'price': 4.99, 'badge': '🍇'},
                    {'name': 'Sourdough', 'price': 4.50, 'badge': '🍞'},
                    {'name': 'Avocados', 'price': 6.99, 'badge': '🥑'},
                    {'name': 'Salmon', 'price': 12.99, 'badge': '🐟'},
                    {'name': 'Bell Peppers', 'price': 3.25, 'badge': '🌶️'},
                    {'name': 'Pasta', 'price': 2.50, 'badge': '🍝'},
                    {'name': 'Lemons', 'price': 2.99, 'badge': '🍋'},
                    {'name': 'Chicken Breast', 'price': 8.99, 'badge': '🐔'},
                    {'name': 'Cucumbers', 'price': 1.75, 'badge': '🥒'},
                    {'name': 'Rice', 'price': 3.99, 'badge': '🍚'},
                    {'name': 'Grapes', 'price': 4.25, 'badge': '🍇'},
                    {'name': 'Olive Oil', 'price': 7.50, 'badge': '🌿'}
                ] %}

                {% for product in sample_products %}
                <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-3">
                    <div class="compact-product-card">
                        <div class="compact-badge">{{ product.badge }}</div>
                        <div class="compact-product-image">
                            <img src="{{ url_for('static', filename='img/default.jpg') }}"
                                 alt="{{ product.name }}" class="img-fluid">
                            <div class="compact-overlay">
                                <i class="fas fa-eye"></i>
                            </div>
                        </div>
                        <div class="compact-product-details">
                            <h5 class="compact-product-title">{{ product.name }}</h5>
                            <div class="compact-price">${{ "%.2f"|format(product.price) }}</div>
                            <button class="compact-btn-cart">
                                <i class="fas fa-cart-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </section>
    
    <!-- Categories Showcase -->
    <section id="categories" class="attractive-section mb-5">
        <div class="container">
            <div class="attractive-section-header">
                <div class="section-badge">Categories</div>
                <h2 class="attractive-section-title">Shop by Category</h2>
                <p class="attractive-section-subtitle">Explore our wide range of quality products</p>
                <div class="section-divider"></div>
            </div>
            
            <div class="row">
                <!-- Fruits & Vegetables Category -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <a href="{{ url_for('shop') }}?category=Fruits" class="category-link-wrapper">
                        <div class="category-card-enhanced fruits-category">
                            <div class="category-background">
                                <div class="category-icon-large">🍎</div>
                                <div class="category-floating-icons">
                                    <span class="floating-fruit-1">🥕</span>
                                    <span class="floating-fruit-2">🥬</span>
                                    <span class="floating-fruit-3">🍌</span>
                                </div>
                            </div>
                            <div class="category-content-enhanced">
                                <h3 class="category-title-enhanced">Fruits & Vegetables</h3>
                                <p class="category-desc-enhanced">Fresh, organic produce</p>
                                <div class="category-stats">
                                    <span class="stat-item">50+ Items</span>
                                    <span class="stat-item">Daily Fresh</span>
                                </div>
                                <div class="category-btn-enhanced">
                                    <i class="fas fa-shopping-basket"></i> Shop Now
                                </div>
                            </div>
                        </div>
                    </a>
                </div>

                <!-- Dairy & Eggs Category -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <a href="{{ url_for('shop') }}?category=Dairy" class="category-link-wrapper">
                        <div class="category-card-enhanced dairy-category">
                            <div class="category-background">
                                <div class="category-icon-large">🥛</div>
                                <div class="category-floating-icons">
                                    <span class="floating-dairy-1">🧀</span>
                                    <span class="floating-dairy-2">🥚</span>
                                    <span class="floating-dairy-3">🧈</span>
                                </div>
                            </div>
                            <div class="category-content-enhanced">
                                <h3 class="category-title-enhanced">Dairy & Eggs</h3>
                                <p class="category-desc-enhanced">Premium dairy products</p>
                                <div class="category-stats">
                                    <span class="stat-item">25+ Items</span>
                                    <span class="stat-item">Farm Fresh</span>
                                </div>
                                <div class="category-btn-enhanced">
                                    <i class="fas fa-shopping-basket"></i> Shop Now
                                </div>
                            </div>
                        </div>
                    </a>
                </div>

                <!-- Bakery Category -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <a href="{{ url_for('shop') }}?category=Bakery" class="category-link-wrapper">
                        <div class="category-card-enhanced bakery-category">
                            <div class="category-background">
                                <div class="category-icon-large">🍞</div>
                                <div class="category-floating-icons">
                                    <span class="floating-bakery-1">🥐</span>
                                    <span class="floating-bakery-2">🥯</span>
                                    <span class="floating-bakery-3">🧁</span>
                                </div>
                            </div>
                            <div class="category-content-enhanced">
                                <h3 class="category-title-enhanced">Bakery</h3>
                                <p class="category-desc-enhanced">Fresh baked goods</p>
                                <div class="category-stats">
                                    <span class="stat-item">30+ Items</span>
                                    <span class="stat-item">Daily Baked</span>
                                </div>
                                <div class="category-btn-enhanced">
                                    <i class="fas fa-shopping-basket"></i> Shop Now
                                </div>
                            </div>
                        </div>
                    </a>
                </div>

                <!-- Meat & Seafood Category -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <a href="{{ url_for('shop') }}?category=Meat" class="category-link-wrapper">
                        <div class="category-card-enhanced meat-category">
                            <div class="category-background">
                                <div class="category-icon-large">🥩</div>
                                <div class="category-floating-icons">
                                    <span class="floating-meat-1">🐟</span>
                                    <span class="floating-meat-2">🐔</span>
                                    <span class="floating-meat-3">🦐</span>
                                </div>
                            </div>
                            <div class="category-content-enhanced">
                                <h3 class="category-title-enhanced">Meat & Seafood</h3>
                                <p class="category-desc-enhanced">Premium quality proteins</p>
                                <div class="category-stats">
                                    <span class="stat-item">20+ Items</span>
                                    <span class="stat-item">Fresh Cut</span>
                                </div>
                                <div class="category-btn-enhanced">
                                    <i class="fas fa-shopping-basket"></i> Shop Now
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Why Choose Us Section -->
    <section id="why-choose-us" class="attractive-section mb-5">
        <div class="container">
            <div class="attractive-section-header">
                <div class="section-badge">Why Us</div>
                <h2 class="attractive-section-title">Why Choose Us</h2>
                <p class="attractive-section-subtitle">Experience quality and convenience</p>
                <div class="section-divider"></div>
            </div>
            
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="simple-feature-card text-center">
                        <div class="simple-feature-icon">
                            <i class="fas fa-shipping-fast"></i>
                        </div>
                        <h4 class="simple-feature-title">Free Delivery</h4>
                        <p class="simple-feature-text">Free delivery on orders over $50. Get your groceries delivered to your doorstep.</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="simple-feature-card text-center">
                        <div class="simple-feature-icon">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <h4 class="simple-feature-title">Fresh Products</h4>
                        <p class="simple-feature-text">We ensure all our products are fresh and of the highest quality.</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="simple-feature-card text-center">
                        <div class="simple-feature-icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <h4 class="simple-feature-title">24/7 Support</h4>
                        <p class="simple-feature-text">Our customer support team is available 24/7 to assist you with any queries.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Attractive Home Page Styles */
    .attractive-hero-section {
        background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
        padding: 4rem 2rem;
        border-radius: 20px;
        position: relative;
        z-index: 1;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(46, 125, 50, 0.1);
        border: 2px solid #c8e6c9;
        margin-top: 1rem;
    }

    .hero-background-pattern {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: radial-gradient(circle at 20% 80%, rgba(76, 175, 80, 0.1) 0%, transparent 50%),
                          radial-gradient(circle at 80% 20%, rgba(129, 199, 132, 0.1) 0%, transparent 50%);
        pointer-events: none;
    }

    .attractive-hero-title {
        font-size: 3rem;
        font-weight: 700;
        color: #1B5E20;
        margin-bottom: 1.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .attractive-hero-subtitle {
        font-size: 1.3rem;
        color: #2E7D32;
        margin-bottom: 2rem;
        line-height: 1.6;
        font-weight: 400;
    }

    .attractive-hero-features {
        margin-bottom: 2.5rem;
    }

    .feature-item {
        display: inline-flex;
        align-items: center;
        background: white;
        padding: 12px 20px;
        border-radius: 25px;
        margin-right: 1rem;
        margin-bottom: 1rem;
        box-shadow: 0 4px 15px rgba(46, 125, 50, 0.15);
        border: 2px solid #c8e6c9;
        transition: all 0.3s ease;
    }

    .feature-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(46, 125, 50, 0.25);
    }

    .feature-item i {
        color: #2E7D32;
        margin-right: 8px;
        font-size: 1.1rem;
    }

    .feature-item span {
        color: #1B5E20;
        font-weight: 500;
    }

    /* Attractive Hero Visual */
    .attractive-hero-visual {
        position: relative;
        padding: 2rem;
    }

    .hero-circle {
        width: 200px;
        height: 200px;
        background: linear-gradient(135deg, #4CAF50, #2E7D32);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        box-shadow: 0 10px 30px rgba(46, 125, 50, 0.3);
        animation: gentle-pulse 3s ease-in-out infinite;
    }

    .hero-circle i {
        font-size: 4rem;
        color: white;
    }

    .floating-icons {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
    }

    .floating-icon-1, .floating-icon-2, .floating-icon-3 {
        position: absolute;
        font-size: 2rem;
        color: #4CAF50;
        animation: float 4s ease-in-out infinite;
    }

    .floating-icon-1 {
        top: 20%;
        left: 10%;
        animation-delay: 0s;
    }

    .floating-icon-2 {
        top: 60%;
        right: 15%;
        animation-delay: 1.5s;
    }

    .floating-icon-3 {
        bottom: 20%;
        left: 20%;
        animation-delay: 3s;
    }

    @keyframes gentle-pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    /* Attractive Section Styles */
    .attractive-section {
        margin-bottom: 4rem;
    }

    .attractive-section-header {
        text-align: center;
        margin-bottom: 3rem;
    }

    .section-badge {
        display: inline-block;
        background: linear-gradient(135deg, #FFC107, #FF9800);
        color: white;
        padding: 8px 20px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: 1rem;
        box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
    }

    .attractive-section-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: #1B5E20;
        margin-bottom: 1rem;
    }

    .attractive-section-subtitle {
        color: #2E7D32;
        font-size: 1.2rem;
        margin-bottom: 1.5rem;
    }

    .section-divider {
        width: 80px;
        height: 4px;
        background: linear-gradient(135deg, #4CAF50, #2E7D32);
        margin: 0 auto;
        border-radius: 2px;
    }

    /* Compact Product Cards */
    .compact-product-card {
        background: white;
        border: 2px solid #e8f5e8;
        border-radius: 12px;
        overflow: hidden;
        transition: all 0.3s ease;
        height: 100%;
        position: relative;
        box-shadow: 0 3px 10px rgba(46, 125, 50, 0.1);
    }

    .compact-product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(46, 125, 50, 0.2);
        border-color: #4CAF50;
    }

    .compact-badge {
        position: absolute;
        top: 8px;
        right: 8px;
        background: linear-gradient(135deg, #FFC107, #FF9800);
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
        z-index: 3;
        box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
    }

    .compact-product-image {
        height: 120px;
        overflow: hidden;
        position: relative;
    }

    .compact-product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .compact-product-card:hover .compact-product-image img {
        transform: scale(1.1);
    }

    .compact-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(46, 125, 50, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .compact-product-card:hover .compact-overlay {
        opacity: 1;
    }

    .compact-overlay i {
        color: white;
        font-size: 1.5rem;
    }

    .compact-product-details {
        padding: 12px;
        text-align: center;
    }

    .compact-product-title {
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: 8px;
        color: #1B5E20;
        line-height: 1.2;
        height: 2.4em;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    .compact-price {
        font-size: 1.1rem;
        font-weight: 700;
        color: #2E7D32;
        margin-bottom: 10px;
    }

    .compact-btn-cart {
        background: linear-gradient(135deg, #4CAF50, #2E7D32);
        color: white;
        border: none;
        border-radius: 20px;
        width: 35px;
        height: 35px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(46, 125, 50, 0.3);
    }

    .compact-btn-cart:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 15px rgba(46, 125, 50, 0.4);
    }

    .compact-btn-cart i {
        font-size: 0.9rem;
    }

    /* Clickable Category Link Wrapper */
    .category-link-wrapper {
        text-decoration: none;
        color: inherit;
        display: block;
        height: 100%;
    }

    .category-link-wrapper:hover {
        text-decoration: none;
        color: inherit;
    }

    /* Enhanced Category Cards */
    .category-card-enhanced {
        background: white;
        border: 2px solid #e8f5e8;
        border-radius: 20px;
        overflow: hidden;
        transition: all 0.4s ease;
        height: 320px;
        position: relative;
        box-shadow: 0 8px 25px rgba(46, 125, 50, 0.1);
        cursor: pointer;
    }

    .category-link-wrapper:hover .category-card-enhanced {
        transform: translateY(-12px) scale(1.02);
        box-shadow: 0 20px 50px rgba(46, 125, 50, 0.25);
        border-color: #4CAF50;
    }

    .category-background {
        height: 180px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
    }

    .fruits-category .category-background {
        background: linear-gradient(135deg, #fff8e1 0%, #f1f8e9 100%);
    }

    .dairy-category .category-background {
        background: linear-gradient(135deg, #f3e5f5 0%, #e8f5e8 100%);
    }

    .bakery-category .category-background {
        background: linear-gradient(135deg, #fff3e0 0%, #f1f8e9 100%);
    }

    .meat-category .category-background {
        background: linear-gradient(135deg, #ffebee 0%, #f1f8e9 100%);
    }

    .category-icon-large {
        font-size: 5rem;
        z-index: 2;
        position: relative;
        animation: gentle-pulse 3s ease-in-out infinite;
    }

    .category-floating-icons {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
    }

    .category-floating-icons span {
        position: absolute;
        font-size: 1.5rem;
        opacity: 0.6;
        animation: float-around 6s ease-in-out infinite;
    }

    .floating-fruit-1 { top: 20%; left: 15%; animation-delay: 0s; }
    .floating-fruit-2 { top: 60%; right: 20%; animation-delay: 2s; }
    .floating-fruit-3 { bottom: 25%; left: 25%; animation-delay: 4s; }

    .floating-dairy-1 { top: 25%; right: 15%; animation-delay: 1s; }
    .floating-dairy-2 { bottom: 30%; left: 20%; animation-delay: 3s; }
    .floating-dairy-3 { top: 50%; left: 10%; animation-delay: 5s; }

    .floating-bakery-1 { top: 15%; left: 20%; animation-delay: 0.5s; }
    .floating-bakery-2 { top: 65%; right: 15%; animation-delay: 2.5s; }
    .floating-bakery-3 { bottom: 20%; right: 25%; animation-delay: 4.5s; }

    .floating-meat-1 { top: 30%; left: 15%; animation-delay: 1.5s; }
    .floating-meat-2 { bottom: 35%; right: 20%; animation-delay: 3.5s; }
    .floating-meat-3 { top: 55%; right: 10%; animation-delay: 5.5s; }

    @keyframes float-around {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        33% { transform: translateY(-8px) rotate(5deg); }
        66% { transform: translateY(4px) rotate(-3deg); }
    }

    .category-content-enhanced {
        padding: 1.5rem;
        text-align: center;
        background: white;
        position: relative;
        z-index: 2;
    }

    .category-title-enhanced {
        font-size: 1.3rem;
        font-weight: 700;
        color: #1B5E20;
        margin-bottom: 0.5rem;
        line-height: 1.2;
    }

    .category-desc-enhanced {
        color: #2E7D32;
        font-size: 0.9rem;
        margin-bottom: 1rem;
        line-height: 1.4;
    }

    .category-stats {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
        margin-bottom: 1.5rem;
    }

    .stat-item {
        background: #e8f5e8;
        color: #2E7D32;
        padding: 4px 10px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .category-btn-enhanced {
        background: linear-gradient(135deg, #4CAF50, #2E7D32);
        color: white;
        border: none;
        border-radius: 20px;
        padding: 10px 20px;
        font-weight: 600;
        display: inline-block;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(46, 125, 50, 0.3);
        font-size: 0.9rem;
        pointer-events: none; /* Prevent button from interfering with card click */
    }

    .category-link-wrapper:hover .category-btn-enhanced {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(46, 125, 50, 0.4);
    }

    /* Add visual feedback for the entire clickable area */
    .category-link-wrapper {
        position: relative;
    }

    .category-link-wrapper::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(76, 175, 80, 0.05);
        opacity: 0;
        transition: opacity 0.3s ease;
        border-radius: 20px;
        z-index: 1;
    }

    .category-link-wrapper:hover::before {
        opacity: 1;
    }

    /* Attractive Buttons */
    .attractive-btn-primary {
        background: linear-gradient(135deg, #4CAF50, #2E7D32);
        color: white;
        border: none;
        border-radius: 25px;
        padding: 15px 30px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(46, 125, 50, 0.3);
        position: relative;
        overflow: hidden;
    }

    .attractive-btn-primary::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .attractive-btn-primary:hover::before {
        left: 100%;
    }

    .attractive-btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(46, 125, 50, 0.4);
        color: white;
        text-decoration: none;
    }

    .attractive-btn-lg {
        padding: 18px 40px;
        font-size: 1.2rem;
    }

    .attractive-btn-secondary {
        background: white;
        color: #2E7D32;
        border: 2px solid #4CAF50;
        border-radius: 25px;
        padding: 12px 24px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(46, 125, 50, 0.1);
    }

    .attractive-btn-secondary:hover {
        background: #4CAF50;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(46, 125, 50, 0.3);
    }

    /* Attractive Feature Cards */
    .simple-feature-card {
        background: white;
        border: 2px solid #e8f5e8;
        border-radius: 15px;
        padding: 2.5rem 2rem;
        transition: all 0.3s ease;
        height: 100%;
        text-align: center;
        box-shadow: 0 4px 15px rgba(46, 125, 50, 0.1);
    }

    .simple-feature-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 15px 35px rgba(46, 125, 50, 0.2);
        border-color: #4CAF50;
    }

    .simple-feature-icon {
        margin-bottom: 1.5rem;
        position: relative;
    }

    .simple-feature-icon i {
        font-size: 3rem;
        color: #2E7D32;
        padding: 20px;
        background: #e8f5e8;
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .simple-feature-card:hover .simple-feature-icon i {
        background: #4CAF50;
        color: white;
        transform: scale(1.1);
    }

    .simple-feature-title {
        font-size: 1.3rem;
        font-weight: 700;
        margin-bottom: 1rem;
        color: #1B5E20;
    }

    .simple-feature-text {
        color: #2E7D32;
        font-size: 1rem;
        line-height: 1.6;
    }

    .simple-no-products {
        text-align: center;
        padding: 4rem 2rem;
        background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
        border-radius: 20px;
        border: 2px solid #c8e6c9;
    }

    .simple-no-products h4 {
        color: #1B5E20;
        margin-bottom: 1rem;
        font-size: 1.5rem;
        font-weight: 700;
    }

    .simple-no-products p {
        color: #2E7D32;
        margin-bottom: 2rem;
        font-size: 1.1rem;
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .product-thumb {
            height: 150px;
        }
        
        .category-card {
            height: 150px;
            margin-bottom: 1rem;
        }
        
        .feature-card {
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
    }
</style>
{% endblock %}