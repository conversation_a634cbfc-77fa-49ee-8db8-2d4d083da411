#!/usr/bin/env python3

"""
Test cart session functionality directly
"""

import sys
import os
import requests
import time

def test_cart_session():
    """Test cart session persistence"""
    
    print("🧪 Testing Cart Session Functionality...")
    
    base_url = "http://127.0.0.1:5000"
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    try:
        # Test 1: Check if server is running
        print("\n1️⃣ Testing server connectivity...")
        response = session.get(f"{base_url}/")
        if response.status_code == 200:
            print("✅ Server is running and accessible")
        else:
            print(f"❌ Server returned status {response.status_code}")
            return False
        
        # Test 2: Check debug cart route
        print("\n2️⃣ Testing debug cart route...")
        debug_response = session.get(f"{base_url}/debug_cart")
        if debug_response.status_code == 200:
            print("✅ Debug cart route accessible")
            print("Debug info:", debug_response.text[:200] + "...")
        else:
            print(f"❌ Debug cart route failed: {debug_response.status_code}")
        
        # Test 3: Add item to cart
        print("\n3️⃣ Testing add to cart...")
        add_response = session.post(f"{base_url}/add_to_cart/Organic Bananas", 
                                   data={'quantity': 2})
        
        if add_response.status_code in [200, 302]:  # 302 is redirect
            print("✅ Add to cart request successful")
        else:
            print(f"❌ Add to cart failed: {add_response.status_code}")
            return False
        
        # Test 4: Check cart contents
        print("\n4️⃣ Testing cart view...")
        cart_response = session.get(f"{base_url}/cart")
        if cart_response.status_code == 200:
            print("✅ Cart page accessible")
            
            # Check if cart has items
            if "Organic Bananas" in cart_response.text:
                print("✅ Cart contains added item!")
            else:
                print("❌ Cart does not contain added item")
                print("Cart content preview:", cart_response.text[:500])
        else:
            print(f"❌ Cart page failed: {cart_response.status_code}")
        
        # Test 5: Check debug info again
        print("\n5️⃣ Testing debug cart after adding item...")
        debug_response2 = session.get(f"{base_url}/debug_cart")
        if debug_response2.status_code == 200:
            print("✅ Debug cart accessible after adding item")
            if "cart_length" in debug_response2.text:
                print("Debug shows cart data")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Is it running on port 5000?")
        return False
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False

def wait_for_server(max_wait=30):
    """Wait for server to start"""
    print(f"⏳ Waiting for server to start (max {max_wait}s)...")
    
    for i in range(max_wait):
        try:
            response = requests.get("http://127.0.0.1:5000/", timeout=2)
            if response.status_code == 200:
                print(f"✅ Server started after {i+1}s")
                return True
        except:
            pass
        time.sleep(1)
        if i % 5 == 4:
            print(f"  Still waiting... ({i+1}s)")
    
    print("❌ Server did not start within timeout")
    return False

if __name__ == "__main__":
    print("🚀 Cart Session Test")
    print("=" * 50)
    
    # Wait for server to be ready
    if wait_for_server():
        # Run the test
        success = test_cart_session()
        
        if success:
            print("\n🎉 Cart session test completed!")
        else:
            print("\n💥 Cart session test failed!")
            sys.exit(1)
    else:
        print("\n💥 Server not available for testing!")
        sys.exit(1)
