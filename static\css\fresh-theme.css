/* Fresh Green Theme for Grocery Store */

/* ===== SMOOTH SCROLLING ===== */
html {
    scroll-behavior: smooth;
    scroll-padding-top: 100px; /* Account for fixed navbar */
}

/* Enhanced smooth scrolling performance */
* {
    -webkit-overflow-scrolling: touch; /* iOS smooth scrolling */
}

/* Optimize scrolling performance */
body {
    -webkit-transform: translateZ(0);
    -moz-transform: translateZ(0);
    -ms-transform: translateZ(0);
    -o-transform: translateZ(0);
    transform: translateZ(0);
}

/* ===== FRESH GREEN COLOR PALETTE ===== */
:root {
    /* Primary Fresh Greens */
    --fresh-primary: #2E7D32;        /* Deep forest green */
    --fresh-primary-light: #4CAF50;  /* Fresh leaf green */
    --fresh-primary-dark: #1B5E20;   /* Dark forest green */
    
    /* Secondary Fresh Colors */
    --fresh-secondary: #66BB6A;      /* Light fresh green */
    --fresh-accent: #81C784;         /* Soft mint green */
    --fresh-lime: #8BC34A;           /* Lime green */
    --fresh-mint: #A5D6A7;           /* Mint green */
    
    /* Nature-inspired Colors */
    --earth-brown: #5D4037;          /* Rich soil brown */
    --sunshine-yellow: #FFC107;      /* Warm sunshine */
    --sky-blue: #03A9F4;             /* Clear sky blue */
    --pure-white: #FFFFFF;           /* Clean white */
    --soft-cream: #F8F9FA;           /* Soft cream background */
    
    /* Gradients */
    --fresh-gradient: linear-gradient(135deg, var(--fresh-primary-light) 0%, var(--fresh-primary) 100%);
    --nature-gradient: linear-gradient(135deg, var(--fresh-lime) 0%, var(--fresh-primary-light) 50%, var(--fresh-primary) 100%);
    --leaf-gradient: linear-gradient(45deg, var(--fresh-mint) 0%, var(--fresh-secondary) 100%);
    
    /* Text Colors */
    --text-primary: #2E2E2E;         /* Dark charcoal */
    --text-secondary: #5F5F5F;       /* Medium gray */
    --text-light: #8F8F8F;           /* Light gray */
    --text-on-green: #FFFFFF;        /* White text on green */
    
    /* Shadows and Effects */
    --fresh-shadow: 0 4px 20px rgba(46, 125, 50, 0.15);
    --fresh-shadow-hover: 0 8px 30px rgba(46, 125, 50, 0.25);
    --organic-shadow: 0 6px 25px rgba(76, 175, 80, 0.2);
    
    /* Border Radius for Organic Feel */
    --border-radius-sm: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 20px;
    --border-radius-xl: 30px;
}

/* ===== GLOBAL FRESH STYLING ===== */
body {
    font-family: 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    background: linear-gradient(135deg, #f8f9fa 0%, #e8f5e8 100%);
    color: var(--text-primary);
    line-height: 1.6;
}

/* ===== FRESH BUTTON STYLES ===== */
.btn-fresh-primary {
    background: var(--fresh-gradient);
    border: none;
    color: var(--text-on-green);
    border-radius: var(--border-radius-md);
    padding: 12px 24px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: var(--fresh-shadow);
    position: relative;
    overflow: hidden;
}

.btn-fresh-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--fresh-shadow-hover);
    color: var(--text-on-green);
}

.btn-fresh-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-fresh-primary:hover::before {
    left: 100%;
}

.btn-fresh-secondary {
    background: var(--leaf-gradient);
    border: 2px solid var(--fresh-secondary);
    color: var(--fresh-primary-dark);
    border-radius: var(--border-radius-md);
    padding: 10px 22px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-fresh-secondary:hover {
    background: var(--fresh-secondary);
    color: var(--text-on-green);
    transform: translateY(-2px);
    box-shadow: var(--fresh-shadow);
}

/* ===== FRESH CARD STYLES ===== */
.card-fresh {
    background: var(--pure-white);
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--fresh-shadow);
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.card-fresh::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--nature-gradient);
}

.card-fresh:hover {
    transform: translateY(-5px);
    box-shadow: var(--organic-shadow);
}

/* ===== FRESH NAVBAR STYLES ===== */
.navbar-fresh {
    background: var(--fresh-gradient) !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(46, 125, 50, 0.1);
    border-bottom: 3px solid var(--fresh-accent);
}

.navbar-fresh .navbar-brand {
    color: var(--text-on-green) !important;
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-fresh .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: var(--border-radius-sm);
    margin: 0 4px;
    padding: 8px 16px !important;
}

.navbar-fresh .nav-link:hover {
    color: var(--text-on-green) !important;
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

/* ===== FRESH SECTION HEADERS ===== */
.section-header-fresh {
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
}

.section-title-fresh {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--fresh-primary-dark);
    margin-bottom: 1rem;
    position: relative;
}

.section-subtitle-fresh {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.section-divider-fresh {
    width: 80px;
    height: 4px;
    background: var(--nature-gradient);
    margin: 0 auto 1.5rem;
    border-radius: 2px;
    position: relative;
}

.section-divider-fresh::before {
    content: '🌿';
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 1.5rem;
    background: var(--soft-cream);
    padding: 0 10px;
}

/* ===== FRESH ANIMATIONS ===== */
@keyframes leafFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
}

@keyframes freshGlow {
    0%, 100% { box-shadow: var(--fresh-shadow); }
    50% { box-shadow: var(--organic-shadow); }
}

.leaf-float {
    animation: leafFloat 3s ease-in-out infinite;
}

.fresh-glow {
    animation: freshGlow 2s ease-in-out infinite;
}

/* ===== ORGANIC SHAPES ===== */
.organic-shape {
    border-radius: 50% 40% 60% 30%;
    background: var(--leaf-gradient);
    position: relative;
    overflow: hidden;
}

.organic-shape::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: leafFloat 4s ease-in-out infinite;
}

/* ===== FRESH VISUAL ELEMENTS ===== */

/* Decorative Leaf Patterns */
.leaf-pattern {
    position: relative;
    overflow: hidden;
}

.leaf-pattern::before {
    content: '🍃 🌿 🍃 🌱 🍃 🌿 🍃 🌱 🍃 🌿 🍃 🌱';
    position: absolute;
    top: -20px;
    left: -100px;
    right: -100px;
    font-size: 1.5rem;
    opacity: 0.1;
    animation: leafFloat 6s ease-in-out infinite;
    pointer-events: none;
}

/* Fresh Icons Enhancement */
.fresh-icon {
    color: var(--fresh-primary);
    transition: all 0.3s ease;
}

.fresh-icon:hover {
    color: var(--fresh-secondary);
    transform: scale(1.2) rotate(5deg);
}

/* Nature-inspired Decorative Elements */
.nature-decoration {
    position: relative;
}

.nature-decoration::after {
    content: '🌱';
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 1.2rem;
    opacity: 0.6;
    animation: leafFloat 3s ease-in-out infinite;
}

/* Fresh Produce Icons */
.produce-icon-fruits::before { content: '🍎'; }
.produce-icon-vegetables::before { content: '🥕'; }
.produce-icon-dairy::before { content: '🥛'; }
.produce-icon-bakery::before { content: '🍞'; }
.produce-icon-organic::before { content: '🌿'; }
.produce-icon-fresh::before { content: '🌱'; }

.produce-icon-fruits::before,
.produce-icon-vegetables::before,
.produce-icon-dairy::before,
.produce-icon-bakery::before,
.produce-icon-organic::before,
.produce-icon-fresh::before {
    font-size: 1.5rem;
    margin-right: 0.5rem;
    animation: leafFloat 4s ease-in-out infinite;
}

/* Fresh Loading Animation */
.fresh-loading {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 3px solid var(--fresh-mint);
    border-radius: 50%;
    border-top-color: var(--fresh-primary);
    animation: freshSpin 1s ease-in-out infinite;
}

@keyframes freshSpin {
    to { transform: rotate(360deg); }
}

/* Organic Dividers */
.organic-divider {
    height: 3px;
    background: var(--nature-gradient);
    border-radius: 50px;
    margin: 2rem 0;
    position: relative;
}

.organic-divider::before {
    content: '🌿';
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--pure-white);
    padding: 0 10px;
    font-size: 1.2rem;
}

/* Fresh Tooltips */
.fresh-tooltip {
    position: relative;
    cursor: pointer;
}

.fresh-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--fresh-primary-dark);
    color: var(--text-on-green);
    padding: 8px 12px;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 1000;
}

.fresh-tooltip:hover::after {
    opacity: 1;
}

/* Fresh Progress Bars */
.fresh-progress {
    height: 8px;
    background: var(--fresh-mint);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.fresh-progress-bar {
    height: 100%;
    background: var(--nature-gradient);
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.fresh-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: freshShimmer 2s infinite;
}

@keyframes freshShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* ===== SMOOTH SCROLLING ENHANCEMENTS ===== */

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--fresh-gradient);
    color: var(--text-on-green);
    border: none;
    border-radius: 50%;
    font-size: 1.2rem;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: var(--fresh-shadow);
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: var(--fresh-primary-dark);
    transform: translateY(-3px);
    box-shadow: var(--fresh-shadow-hover);
}

/* Smooth Scroll Indicators */
.scroll-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--fresh-mint);
    z-index: 9999;
}

.scroll-progress {
    height: 100%;
    background: var(--nature-gradient);
    width: 0%;
    transition: width 0.1s ease;
}

/* Smooth Section Transitions */
section {
    scroll-margin-top: 100px;
}

/* Enhanced Link Smooth Scrolling */
a[href^="#"] {
    transition: all 0.3s ease;
}

a[href^="#"]:hover {
    transform: translateY(-1px);
}

/* Smooth Animations for Page Load */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.6s ease;
}

.slide-in-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.slide-in-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.6s ease;
}

.slide-in-right.visible {
    opacity: 1;
    transform: translateX(0);
}

/* ===== RESPONSIVE FRESH DESIGN ===== */

/* Large Desktop (1200px and up) */
@media (min-width: 1200px) {
    .section-title-fresh {
        font-size: 3rem;
    }

    .hero-section {
        padding: 4rem 2rem;
    }

    .category-card-fresh {
        height: 320px;
    }

    .product-card-fresh {
        transition: all 0.3s ease;
    }

    .product-card-fresh:hover {
        transform: translateY(-10px) scale(1.03);
    }
}

/* Desktop (992px to 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
    .section-title-fresh {
        font-size: 2.5rem;
    }

    .hero-section {
        padding: 3rem 1.5rem;
    }

    .category-card-fresh {
        height: 280px;
    }

    .navbar-fresh .nav-link {
        padding: 8px 12px !important;
        margin: 0 2px;
    }
}

/* Tablet (768px to 991px) */
@media (min-width: 768px) and (max-width: 991px) {
    .section-title-fresh {
        font-size: 2.2rem;
    }

    .section-subtitle-fresh {
        font-size: 1.1rem;
    }

    .hero-section {
        padding: 2.5rem 1rem;
        text-align: center;
    }

    .hero-section .organic-shape {
        width: 150px;
        height: 150px;
        margin: 2rem auto 0;
    }

    .category-card-fresh {
        height: 240px;
        margin-bottom: 2rem;
    }

    .category-title-fresh {
        font-size: 1.2rem;
    }

    .product-card-fresh {
        margin-bottom: 2rem;
    }

    .product-thumb-fresh {
        height: 180px;
    }

    .navbar-fresh .navbar-brand {
        font-size: 1.4rem;
    }

    .btn-fresh-primary,
    .btn-fresh-secondary {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}

/* Mobile Large (576px to 767px) */
@media (min-width: 576px) and (max-width: 767px) {
    .section-title-fresh {
        font-size: 1.8rem;
        text-align: center;
    }

    .section-subtitle-fresh {
        font-size: 1rem;
        text-align: center;
    }

    .hero-section {
        padding: 2rem 1rem;
        text-align: center;
    }

    .hero-section .row {
        flex-direction: column;
    }

    .hero-section .organic-shape {
        width: 120px;
        height: 120px;
        margin: 1.5rem auto;
    }

    .category-card-fresh {
        height: 200px;
        margin-bottom: 1.5rem;
    }

    .category-title-fresh {
        font-size: 1.1rem;
    }

    .category-desc-fresh {
        font-size: 0.8rem;
    }

    .product-card-fresh {
        margin-bottom: 1.5rem;
    }

    .product-thumb-fresh {
        height: 160px;
    }

    .product-title-fresh {
        font-size: 1rem;
    }

    .product-price-fresh {
        font-size: 1.2rem;
    }

    .feature-card-fresh {
        padding: 1.5rem !important;
        margin-bottom: 1.5rem;
    }

    .feature-title-fresh {
        font-size: 1.1rem;
    }

    .navbar-fresh .navbar-brand {
        font-size: 1.3rem;
    }

    .btn-fresh-primary,
    .btn-fresh-secondary {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* Mobile Small (up to 575px) */
@media (max-width: 575px) {
    .section-title-fresh {
        font-size: 1.5rem;
        text-align: center;
        line-height: 1.3;
    }

    .section-subtitle-fresh {
        font-size: 0.9rem;
        text-align: center;
    }

    .hero-section {
        padding: 1.5rem 0.5rem;
        text-align: center;
    }

    .hero-section h1 {
        font-size: 1.8rem !important;
        line-height: 1.3;
    }

    .hero-section .lead {
        font-size: 1rem !important;
    }

    .hero-section .organic-shape {
        width: 100px;
        height: 100px;
        margin: 1rem auto;
    }

    .category-card-fresh {
        height: 180px;
        margin-bottom: 1rem;
    }

    .category-title-fresh {
        font-size: 1rem;
    }

    .category-desc-fresh {
        font-size: 0.75rem;
        display: none; /* Hide on very small screens */
    }

    .category-icon-fresh {
        font-size: 2.5rem !important;
    }

    .product-card-fresh {
        margin-bottom: 1rem;
    }

    .product-thumb-fresh {
        height: 140px;
    }

    .product-title-fresh {
        font-size: 0.95rem;
    }

    .product-price-fresh {
        font-size: 1.1rem;
    }

    .product-description-fresh {
        height: 35px;
        font-size: 0.8rem;
    }

    .feature-card-fresh {
        padding: 1rem !important;
        margin-bottom: 1rem;
    }

    .feature-title-fresh {
        font-size: 1rem;
    }

    .feature-text-fresh {
        font-size: 0.85rem;
    }

    .navbar-fresh .navbar-brand {
        font-size: 1.2rem;
    }

    .navbar-fresh .nav-link {
        padding: 6px 10px !important;
        font-size: 0.9rem;
    }

    .btn-fresh-primary,
    .btn-fresh-secondary {
        width: 100%;
        padding: 8px 16px;
        font-size: 0.85rem;
        margin-bottom: 0.5rem;
    }

    /* Mobile-specific fresh elements */
    .leaf-pattern::before {
        font-size: 1rem;
        opacity: 0.05;
    }

    .organic-divider {
        margin: 1rem 0;
    }

    .fresh-tooltip {
        display: none; /* Hide tooltips on mobile */
    }

    /* Mobile navigation improvements */
    .navbar-toggler {
        border: 2px solid rgba(255, 255, 255, 0.5);
        padding: 4px 8px;
    }

    .navbar-toggler:focus {
        box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    .product-card-fresh:hover,
    .category-card-fresh:hover,
    .feature-card-fresh:hover {
        transform: none;
    }

    .btn-fresh-primary,
    .btn-fresh-secondary {
        padding: 12px 24px; /* Larger touch targets */
    }

    .fresh-icon:hover {
        transform: none;
    }

    /* Disable hover animations on touch devices */
    .leaf-float {
        animation: none;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .organic-shape {
        border-radius: 50% 40% 60% 30%;
    }

    .section-divider-fresh {
        height: 5px;
    }
}

/* Landscape orientation on mobile */
@media (max-width: 767px) and (orientation: landscape) {
    .hero-section {
        padding: 1rem 0.5rem;
    }

    .category-card-fresh {
        height: 160px;
    }

    .feature-card-fresh {
        padding: 1rem !important;
    }
}

/* Print styles for fresh theme */
@media print {
    .navbar-fresh,
    .btn-fresh-primary,
    .btn-fresh-secondary,
    .leaf-float,
    .fresh-glow {
        display: none !important;
    }

    .section-title-fresh {
        color: #000 !important;
    }

    .product-card-fresh {
        border: 1px solid #ccc !important;
        box-shadow: none !important;
    }
}
