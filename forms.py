from flask_wtf import F<PERSON>kForm
from flask_wtf.file import <PERSON><PERSON>ield, FileAllowed, FileRequired
from wtforms import StringField, PasswordField, SubmitField, BooleanField, TextAreaField, SelectField, DecimalField, IntegerField, HiddenField
from wtforms.validators import DataRequired, Email, EqualTo, Length, ValidationError, Optional, NumberRange
from models import User, Category, Product

class LoginForm(FlaskForm):
    """Login form for user authentication"""
    
    username = StringField('Username or Email', validators=[
        DataRequired(message='Username or email is required'),
        Length(min=3, max=80, message='Username must be between 3 and 80 characters')
    ])
    
    password = PasswordField('Password', validators=[
        DataRequired(message='Password is required')
    ])
    
    remember_me = BooleanField('Remember Me')
    
    submit = SubmitField('Sign In')


class RegistrationForm(FlaskForm):
    """Registration form for new user signup"""
    
    username = <PERSON><PERSON>ield('Username', validators=[
        DataRequired(message='Username is required'),
        Length(min=3, max=80, message='Username must be between 3 and 80 characters')
    ])
    
    email = StringField('Email', validators=[
        DataRequired(message='Email is required'),
        Email(message='Please enter a valid email address'),
        Length(max=120, message='Email must be less than 120 characters')
    ])
    
    first_name = StringField('First Name', validators=[
        DataRequired(message='First name is required'),
        Length(min=2, max=50, message='First name must be between 2 and 50 characters')
    ])
    
    last_name = StringField('Last Name', validators=[
        DataRequired(message='Last name is required'),
        Length(min=2, max=50, message='Last name must be between 2 and 50 characters')
    ])
    
    phone = StringField('Phone Number', validators=[
        Optional(),
        Length(max=20, message='Phone number must be less than 20 characters')
    ])
    
    password = PasswordField('Password', validators=[
        DataRequired(message='Password is required'),
        Length(min=6, max=128, message='Password must be between 6 and 128 characters')
    ])
    
    password2 = PasswordField('Confirm Password', validators=[
        DataRequired(message='Please confirm your password'),
        EqualTo('password', message='Passwords must match')
    ])
    
    submit = SubmitField('Create Account')
    
    def validate_username(self, username):
        """Check if username is already taken"""
        user = User.query.filter_by(username=username.data).first()
        if user:
            raise ValidationError('Username already exists. Please choose a different one.')
    
    def validate_email(self, email):
        """Check if email is already registered"""
        user = User.query.filter_by(email=email.data).first()
        if user:
            raise ValidationError('Email already registered. Please use a different email or login.')


class ProfileForm(FlaskForm):
    """Profile form for updating user information"""
    
    first_name = StringField('First Name', validators=[
        DataRequired(message='First name is required'),
        Length(min=2, max=50, message='First name must be between 2 and 50 characters')
    ])
    
    last_name = StringField('Last Name', validators=[
        DataRequired(message='Last name is required'),
        Length(min=2, max=50, message='Last name must be between 2 and 50 characters')
    ])
    
    email = StringField('Email', validators=[
        DataRequired(message='Email is required'),
        Email(message='Please enter a valid email address'),
        Length(max=120, message='Email must be less than 120 characters')
    ])
    
    phone = StringField('Phone Number', validators=[
        Optional(),
        Length(max=20, message='Phone number must be less than 20 characters')
    ])
    
    address = TextAreaField('Address', validators=[
        Optional(),
        Length(max=500, message='Address must be less than 500 characters')
    ])
    
    city = StringField('City', validators=[
        Optional(),
        Length(max=50, message='City must be less than 50 characters')
    ])
    
    state = StringField('State', validators=[
        Optional(),
        Length(max=50, message='State must be less than 50 characters')
    ])
    
    zip_code = StringField('ZIP Code', validators=[
        Optional(),
        Length(max=10, message='ZIP code must be less than 10 characters')
    ])
    
    submit = SubmitField('Update Profile')
    
    def __init__(self, original_email, *args, **kwargs):
        super(ProfileForm, self).__init__(*args, **kwargs)
        self.original_email = original_email
    
    def validate_email(self, email):
        """Check if email is already taken by another user"""
        if email.data != self.original_email:
            user = User.query.filter_by(email=email.data).first()
            if user:
                raise ValidationError('Email already registered. Please use a different email.')


class ChangePasswordForm(FlaskForm):
    """Form for changing user password"""
    
    current_password = PasswordField('Current Password', validators=[
        DataRequired(message='Current password is required')
    ])
    
    new_password = PasswordField('New Password', validators=[
        DataRequired(message='New password is required'),
        Length(min=6, max=128, message='Password must be between 6 and 128 characters')
    ])
    
    new_password2 = PasswordField('Confirm New Password', validators=[
        DataRequired(message='Please confirm your new password'),
        EqualTo('new_password', message='Passwords must match')
    ])
    
    submit = SubmitField('Change Password')


class AdminUserForm(FlaskForm):
    """Admin form for managing users"""
    
    username = StringField('Username', validators=[
        DataRequired(message='Username is required'),
        Length(min=3, max=80, message='Username must be between 3 and 80 characters')
    ])
    
    email = StringField('Email', validators=[
        DataRequired(message='Email is required'),
        Email(message='Please enter a valid email address'),
        Length(max=120, message='Email must be less than 120 characters')
    ])
    
    first_name = StringField('First Name', validators=[
        DataRequired(message='First name is required'),
        Length(min=2, max=50, message='First name must be between 2 and 50 characters')
    ])
    
    last_name = StringField('Last Name', validators=[
        DataRequired(message='Last name is required'),
        Length(min=2, max=50, message='Last name must be between 2 and 50 characters')
    ])
    
    role = SelectField('Role', choices=[
        ('customer', 'Customer'),
        ('staff', 'Staff'),
        ('admin', 'Admin')
    ], validators=[DataRequired()])
    
    is_active = BooleanField('Active')
    is_verified = BooleanField('Verified')
    
    submit = SubmitField('Save User')


class ForgotPasswordForm(FlaskForm):
    """Form for password reset request"""
    
    email = StringField('Email', validators=[
        DataRequired(message='Email is required'),
        Email(message='Please enter a valid email address')
    ])
    
    submit = SubmitField('Reset Password')
    
    def validate_email(self, email):
        """Check if email exists in the system"""
        user = User.query.filter_by(email=email.data).first()
        if not user:
            raise ValidationError('No account found with that email address.')


class ResetPasswordForm(FlaskForm):
    """Form for resetting password with token"""

    password = PasswordField('New Password', validators=[
        DataRequired(message='Password is required'),
        Length(min=6, max=128, message='Password must be between 6 and 128 characters')
    ])

    password2 = PasswordField('Confirm Password', validators=[
        DataRequired(message='Please confirm your password'),
        EqualTo('password', message='Passwords must match')
    ])

    submit = SubmitField('Reset Password')


# ===== PRODUCT MANAGEMENT FORMS =====

class CategoryForm(FlaskForm):
    """Form for creating and editing categories"""

    name = StringField('Category Name', validators=[
        DataRequired(message='Category name is required'),
        Length(min=2, max=100, message='Category name must be between 2 and 100 characters')
    ])

    description = TextAreaField('Description', validators=[
        Optional(),
        Length(max=500, message='Description must be less than 500 characters')
    ])

    parent_id = SelectField('Parent Category', coerce=str, validators=[Optional()])

    icon = StringField('Icon Class', validators=[
        Optional(),
        Length(max=50, message='Icon class must be less than 50 characters')
    ], description='Font Awesome icon class (e.g., fas fa-apple-alt)')

    color = StringField('Color', validators=[
        Optional(),
        Length(min=7, max=7, message='Color must be a valid hex code')
    ], description='Hex color code (e.g., #4CAF50)')

    is_active = BooleanField('Active', default=True)

    sort_order = IntegerField('Sort Order', validators=[
        Optional(),
        NumberRange(min=0, message='Sort order must be 0 or greater')
    ], default=0)

    meta_title = StringField('Meta Title', validators=[
        Optional(),
        Length(max=200, message='Meta title must be less than 200 characters')
    ])

    meta_description = TextAreaField('Meta Description', validators=[
        Optional(),
        Length(max=300, message='Meta description must be less than 300 characters')
    ])

    submit = SubmitField('Save Category')

    def __init__(self, category=None, *args, **kwargs):
        super(CategoryForm, self).__init__(*args, **kwargs)

        # Populate parent category choices
        categories = Category.query.filter_by(is_active=True).order_by(Category.name).all()
        self.parent_id.choices = [('', 'No Parent')] + [(c.id, c.name) for c in categories if not category or c.id != category.id]

    def validate_name(self, name):
        """Check if category name is unique"""
        category = Category.query.filter_by(name=name.data).first()
        if category and (not hasattr(self, 'category') or category.id != self.category.id):
            raise ValidationError('Category name already exists. Please choose a different name.')


class ProductForm(FlaskForm):
    """Form for creating and editing products"""

    name = StringField('Product Name', validators=[
        DataRequired(message='Product name is required'),
        Length(min=2, max=200, message='Product name must be between 2 and 200 characters')
    ])

    category_id = SelectField('Category', coerce=str, validators=[
        DataRequired(message='Please select a category')
    ])

    sku = StringField('SKU', validators=[
        Optional(),
        Length(max=50, message='SKU must be less than 50 characters')
    ])

    barcode = StringField('Barcode', validators=[
        Optional(),
        Length(max=50, message='Barcode must be less than 50 characters')
    ])

    description = TextAreaField('Description', validators=[
        Optional(),
        Length(max=2000, message='Description must be less than 2000 characters')
    ])

    short_description = StringField('Short Description', validators=[
        Optional(),
        Length(max=500, message='Short description must be less than 500 characters')
    ])

    price = DecimalField('Price', validators=[
        DataRequired(message='Price is required'),
        NumberRange(min=0.01, message='Price must be greater than 0')
    ], places=2)

    cost_price = DecimalField('Cost Price', validators=[
        Optional(),
        NumberRange(min=0, message='Cost price must be 0 or greater')
    ], places=2)

    compare_price = DecimalField('Compare Price', validators=[
        Optional(),
        NumberRange(min=0, message='Compare price must be 0 or greater')
    ], places=2, description='Original price for showing discounts')

    stock_quantity = IntegerField('Stock Quantity', validators=[
        DataRequired(message='Stock quantity is required'),
        NumberRange(min=0, message='Stock quantity must be 0 or greater')
    ], default=0)

    low_stock_threshold = IntegerField('Low Stock Threshold', validators=[
        Optional(),
        NumberRange(min=0, message='Low stock threshold must be 0 or greater')
    ], default=10)

    track_inventory = BooleanField('Track Inventory', default=True)
    allow_backorder = BooleanField('Allow Backorder', default=False)

    weight = DecimalField('Weight (kg)', validators=[
        Optional(),
        NumberRange(min=0, message='Weight must be 0 or greater')
    ], places=3)

    dimensions = StringField('Dimensions (L×W×H cm)', validators=[
        Optional(),
        Length(max=100, message='Dimensions must be less than 100 characters')
    ])

    unit = SelectField('Unit', choices=[
        ('', 'Select Unit'),
        ('piece', 'Piece'),
        ('kg', 'Kilogram'),
        ('g', 'Gram'),
        ('liter', 'Liter'),
        ('ml', 'Milliliter'),
        ('dozen', 'Dozen'),
        ('pack', 'Pack'),
        ('box', 'Box'),
        ('bunch', 'Bunch')
    ], validators=[Optional()])

    unit_quantity = DecimalField('Unit Quantity', validators=[
        Optional(),
        NumberRange(min=0.001, message='Unit quantity must be greater than 0')
    ], places=3, default=1)

    is_active = BooleanField('Active', default=True)
    is_featured = BooleanField('Featured Product', default=False)
    requires_shipping = BooleanField('Requires Shipping', default=True)

    ingredients = TextAreaField('Ingredients', validators=[
        Optional(),
        Length(max=1000, message='Ingredients must be less than 1000 characters')
    ])

    nutritional_info = TextAreaField('Nutritional Information', validators=[
        Optional(),
        Length(max=1000, message='Nutritional info must be less than 1000 characters')
    ])

    tags = StringField('Tags', validators=[
        Optional(),
        Length(max=500, message='Tags must be less than 500 characters')
    ], description='Comma-separated tags for search')

    meta_title = StringField('Meta Title', validators=[
        Optional(),
        Length(max=200, message='Meta title must be less than 200 characters')
    ])

    meta_description = TextAreaField('Meta Description', validators=[
        Optional(),
        Length(max=300, message='Meta description must be less than 300 characters')
    ])

    submit = SubmitField('Save Product')

    def __init__(self, product=None, *args, **kwargs):
        super(ProductForm, self).__init__(*args, **kwargs)

        # Populate category choices
        categories = Category.query.filter_by(is_active=True).order_by(Category.name).all()
        self.category_id.choices = [('', 'Select Category')] + [(c.id, c.name) for c in categories]

        self.product = product

    def validate_sku(self, sku):
        """Check if SKU is unique"""
        if sku.data:
            product = Product.query.filter_by(sku=sku.data).first()
            if product and (not self.product or product.id != self.product.id):
                raise ValidationError('SKU already exists. Please choose a different SKU.')

    def validate_barcode(self, barcode):
        """Check if barcode is unique"""
        if barcode.data:
            product = Product.query.filter_by(barcode=barcode.data).first()
            if product and (not self.product or product.id != self.product.id):
                raise ValidationError('Barcode already exists. Please choose a different barcode.')


class ProductImageForm(FlaskForm):
    """Form for uploading product images"""

    image = FileField('Product Image', validators=[
        FileRequired(message='Please select an image file'),
        FileAllowed(['jpg', 'jpeg', 'png', 'gif', 'webp'], 'Only image files are allowed')
    ])

    alt_text = StringField('Alt Text', validators=[
        Optional(),
        Length(max=255, message='Alt text must be less than 255 characters')
    ])

    is_main = BooleanField('Set as Main Image', default=False)

    submit = SubmitField('Upload Image')


class InventoryAdjustmentForm(FlaskForm):
    """Form for adjusting product inventory"""

    product_id = HiddenField('Product ID', validators=[DataRequired()])

    adjustment_type = SelectField('Adjustment Type', choices=[
        ('add', 'Add Stock'),
        ('remove', 'Remove Stock'),
        ('set', 'Set Stock Level')
    ], validators=[DataRequired()])

    quantity = IntegerField('Quantity', validators=[
        DataRequired(message='Quantity is required'),
        NumberRange(min=1, message='Quantity must be greater than 0')
    ])

    reason = SelectField('Reason', choices=[
        ('received', 'Stock Received'),
        ('sold', 'Stock Sold'),
        ('damaged', 'Damaged/Expired'),
        ('returned', 'Customer Return'),
        ('adjustment', 'Manual Adjustment'),
        ('audit', 'Inventory Audit'),
        ('other', 'Other')
    ], validators=[DataRequired()])

    notes = TextAreaField('Notes', validators=[
        Optional(),
        Length(max=500, message='Notes must be less than 500 characters')
    ])

    submit = SubmitField('Update Inventory')


class ProductSearchForm(FlaskForm):
    """Form for searching and filtering products"""

    search = StringField('Search Products', validators=[Optional()])

    category_id = SelectField('Category', coerce=str, validators=[Optional()])

    min_price = DecimalField('Min Price', validators=[
        Optional(),
        NumberRange(min=0, message='Price must be 0 or greater')
    ], places=2)

    max_price = DecimalField('Max Price', validators=[
        Optional(),
        NumberRange(min=0, message='Price must be 0 or greater')
    ], places=2)

    in_stock_only = BooleanField('In Stock Only', default=False)
    featured_only = BooleanField('Featured Only', default=False)
    active_only = BooleanField('Active Only', default=True)

    sort_by = SelectField('Sort By', choices=[
        ('name_asc', 'Name (A-Z)'),
        ('name_desc', 'Name (Z-A)'),
        ('price_asc', 'Price (Low to High)'),
        ('price_desc', 'Price (High to Low)'),
        ('created_desc', 'Newest First'),
        ('created_asc', 'Oldest First'),
        ('stock_asc', 'Stock (Low to High)'),
        ('stock_desc', 'Stock (High to Low)')
    ], default='name_asc')

    submit = SubmitField('Search')

    def __init__(self, *args, **kwargs):
        super(ProductSearchForm, self).__init__(*args, **kwargs)

        # Populate category choices
        categories = Category.query.filter_by(is_active=True).order_by(Category.name).all()
        self.category_id.choices = [('', 'All Categories')] + [(c.id, c.name) for c in categories]
