#!/usr/bin/env python3

"""
Test admin fix by simulating the admin dashboard function
"""

import sys
import os
import json

def load_orders():
    """Load orders from JSON file"""
    try:
        if os.path.exists('orders.json'):
            with open('orders.json', 'r') as f:
                return json.load(f)
        return []
    except Exception as e:
        print(f"Error loading orders: {e}")
        return []

def load_grocery_data():
    """Load grocery data from JSON file"""
    try:
        if os.path.exists('grocery_data.json'):
            with open('grocery_data.json', 'r') as f:
                return json.load(f)
        return []
    except Exception as e:
        print(f"Error loading grocery data: {e}")
        return []

def test_admin_dashboard_logic():
    """Test the admin dashboard logic"""
    print("🧪 Testing Admin Dashboard Logic...")
    
    try:
        # Simulate the admin dashboard function logic
        orders = load_orders()
        items = load_grocery_data()
        
        print(f"📦 Loaded {len(orders)} orders")
        print(f"🛒 Loaded {len(items)} items")
        
        # Calculate order counts by status
        pending_count = len([o for o in orders if o.get('status') == 'pending'])
        confirmed_count = len([o for o in orders if o.get('status') == 'confirmed'])
        delivered_count = len([o for o in orders if o.get('status') == 'delivered'])
        
        print(f"📊 Order Statistics:")
        print(f"   Pending: {pending_count}")
        print(f"   Confirmed: {confirmed_count}")
        print(f"   Delivered: {delivered_count}")
        
        # Show recent orders
        recent_orders = orders[:5]
        print(f"\n📋 Recent Orders ({len(recent_orders)}):")
        for i, order in enumerate(recent_orders, 1):
            order_id = order.get('order_id', 'N/A')[:8] if order.get('order_id') else 'N/A'
            status = order.get('status', 'unknown')
            total = order.get('total', 0)
            print(f"   {i}. {order_id}... - {status} - ${total:.2f}")
        
        # Show some items
        recent_items = items[:5]
        print(f"\n🛍️ Sample Items ({len(recent_items)}):")
        for i, item in enumerate(recent_items, 1):
            name = item.get('name', 'Unknown')
            price = item.get('price', 0)
            quantity = item.get('quantity', 0)
            print(f"   {i}. {name} - ${price:.2f} (Stock: {quantity})")
        
        print("\n✅ Admin dashboard logic test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error in admin dashboard logic: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Admin Fix Test")
    print("=" * 40)
    
    success = test_admin_dashboard_logic()
    
    if success:
        print("\n🎉 Admin dashboard logic is working correctly!")
        print("The admin page should now work without errors.")
    else:
        print("\n💥 Admin dashboard logic test failed!")
        sys.exit(1)
