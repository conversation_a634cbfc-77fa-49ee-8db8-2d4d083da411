{% extends "base.html" %}
{% block title %}Create Account - Fresh Grocery{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <!-- Registration Card -->
            <div class="auth-card">
                <div class="auth-header">
                    <div class="auth-icon">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <h2 class="auth-title">Join Fresh Grocery!</h2>
                    <p class="auth-subtitle">Create your account and start shopping fresh</p>
                </div>
                
                <div class="auth-body">
                    <form method="POST" action="{{ url_for('register') }}">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <!-- First Name -->
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.first_name.label(class="form-label auth-label") }}
                                    {{ form.first_name(class="form-control auth-input" + (" is-invalid" if form.first_name.errors else ""), placeholder="Enter your first name") }}
                                    {% if form.first_name.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.first_name.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <!-- Last Name -->
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.last_name.label(class="form-label auth-label") }}
                                    {{ form.last_name(class="form-control auth-input" + (" is-invalid" if form.last_name.errors else ""), placeholder="Enter your last name") }}
                                    {% if form.last_name.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.last_name.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Username -->
                        <div class="form-group mb-3">
                            {{ form.username.label(class="form-label auth-label") }}
                            {{ form.username(class="form-control auth-input" + (" is-invalid" if form.username.errors else ""), placeholder="Choose a unique username") }}
                            {% if form.username.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.username.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Email -->
                        <div class="form-group mb-3">
                            {{ form.email.label(class="form-label auth-label") }}
                            {{ form.email(class="form-control auth-input" + (" is-invalid" if form.email.errors else ""), placeholder="Enter your email address") }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Phone (Optional) -->
                        <div class="form-group mb-3">
                            {{ form.phone.label(class="form-label auth-label") }}
                            {{ form.phone(class="form-control auth-input" + (" is-invalid" if form.phone.errors else ""), placeholder="Enter your phone number (optional)") }}
                            {% if form.phone.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.phone.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="row">
                            <!-- Password -->
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.password.label(class="form-label auth-label") }}
                                    <div class="password-input-wrapper">
                                        {{ form.password(class="form-control auth-input" + (" is-invalid" if form.password.errors else ""), placeholder="Create a password") }}
                                        <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                            <i class="fas fa-eye" id="password-eye"></i>
                                        </button>
                                    </div>
                                    {% if form.password.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.password.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <!-- Confirm Password -->
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.password2.label(class="form-label auth-label") }}
                                    <div class="password-input-wrapper">
                                        {{ form.password2(class="form-control auth-input" + (" is-invalid" if form.password2.errors else ""), placeholder="Confirm your password") }}
                                        <button type="button" class="password-toggle" onclick="togglePassword('password2')">
                                            <i class="fas fa-eye" id="password2-eye"></i>
                                        </button>
                                    </div>
                                    {% if form.password2.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.password2.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Terms and Conditions -->
                        <div class="form-group mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="terms" required>
                                <label class="form-check-label" for="terms">
                                    I agree to the <a href="#" class="auth-link">Terms of Service</a> and <a href="#" class="auth-link">Privacy Policy</a>
                                </label>
                            </div>
                        </div>
                        
                        <!-- Submit Button -->
                        <div class="form-group mb-4">
                            {{ form.submit(class="btn auth-btn-primary w-100") }}
                        </div>
                    </form>
                    
                    <!-- Additional Links -->
                    <div class="auth-links">
                        <div class="auth-divider">
                            <span>Already have an account?</span>
                        </div>
                        <div class="text-center">
                            <a href="{{ url_for('login') }}" class="btn auth-btn-secondary w-100">
                                <i class="fas fa-sign-in-alt"></i> Sign In to Your Account
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Authentication Styles */
.auth-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(46, 125, 50, 0.15);
    border: 2px solid #e8f5e8;
    overflow: hidden;
}

.auth-header {
    background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
    padding: 2rem;
    text-align: center;
    border-bottom: 1px solid #e8f5e8;
}

.auth-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    box-shadow: 0 8px 25px rgba(46, 125, 50, 0.3);
}

.auth-icon i {
    font-size: 2rem;
    color: white;
}

.auth-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: #1B5E20;
    margin-bottom: 0.5rem;
}

.auth-subtitle {
    color: #2E7D32;
    font-size: 1rem;
    margin: 0;
}

.auth-body {
    padding: 2rem;
}

.auth-label {
    color: #1B5E20;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.auth-input {
    border: 2px solid #e8f5e8;
    border-radius: 10px;
    padding: 12px 16px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.auth-input:focus {
    border-color: #4CAF50;
    box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
}

.password-input-wrapper {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 4px;
}

.password-toggle:hover {
    color: #2E7D32;
}

.auth-btn-primary {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    border: none;
    border-radius: 10px;
    padding: 12px 24px;
    font-weight: 600;
    font-size: 1rem;
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(46, 125, 50, 0.3);
}

.auth-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(46, 125, 50, 0.4);
}

.auth-btn-secondary {
    background: transparent;
    border: 2px solid #4CAF50;
    border-radius: 10px;
    padding: 10px 24px;
    font-weight: 600;
    color: #2E7D32;
    transition: all 0.3s ease;
}

.auth-btn-secondary:hover {
    background: #4CAF50;
    color: white;
    transform: translateY(-2px);
}

.auth-links {
    margin-top: 1.5rem;
}

.auth-link {
    color: #2E7D32;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.auth-link:hover {
    color: #1B5E20;
    text-decoration: underline;
}

.auth-divider {
    text-align: center;
    margin: 1.5rem 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e8f5e8;
}

.auth-divider span {
    background: white;
    padding: 0 1rem;
    color: #6c757d;
    font-size: 0.9rem;
}
</style>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const eye = document.getElementById(fieldId + '-eye');
    
    if (field.type === 'password') {
        field.type = 'text';
        eye.classList.remove('fa-eye');
        eye.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        eye.classList.remove('fa-eye-slash');
        eye.classList.add('fa-eye');
    }
}
</script>
{% endblock %}
