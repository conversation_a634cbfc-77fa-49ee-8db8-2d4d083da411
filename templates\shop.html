{% extends "base.html" %}
{% block title %}Fresh Shop - Organic Groceries{% endblock %}
{% block content %}
<div class="container py-4">
    <!-- Simple Shop Header -->
    <div class="simple-shop-header mb-4">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="simple-shop-title">Shop Fresh Products</h1>
                <p class="simple-shop-subtitle">Quality groceries for your daily needs</p>
            </div>
            <div class="col-md-4 text-end">
                <span class="simple-item-count">{{ items|length }} items available</span>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Simple Sidebar with Categories -->
        <div class="col-lg-3 mb-4">
            <div class="simple-sidebar">
                <div class="simple-sidebar-header">
                    <h3>Categories</h3>
                </div>
                <div class="simple-sidebar-body">
                    <ul class="simple-category-list">
                        <li class="simple-category-item {% if not request.args.get('category') %}active{% endif %}">
                            <a href="{{ url_for('shop') }}">All Products</a>
                        </li>
                        {% for category in categories %}
                        <li class="simple-category-item {% if request.args.get('category_id') == category.id %}active{% endif %}">
                            <a href="{{ url_for('shop') }}?category_id={{ category.id }}">{{ category.name }}</a>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>

            <!-- Simple Price Filter -->
            <div class="simple-sidebar mt-4">
                <div class="simple-sidebar-header">
                    <h3>Price Filter</h3>
                </div>
                <div class="simple-sidebar-body">
                    <form action="{{ url_for('shop') }}" method="GET" class="simple-price-form">
                        <div class="mb-3">
                            <label for="min-price" class="simple-label">Min Price:</label>
                            <input type="number" class="simple-input" id="min-price" name="min_price"
                                   value="{{ request.args.get('min_price', '') }}" min="0" step="0.01" placeholder="$0.00">
                        </div>
                        <div class="mb-3">
                            <label for="max-price" class="simple-label">Max Price:</label>
                            <input type="number" class="simple-input" id="max-price" name="max_price"
                                   value="{{ request.args.get('max_price', '') }}" min="0" step="0.01" placeholder="$999.99">
                        </div>
                        <button type="submit" class="simple-btn-primary w-100">Apply Filter</button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Simple Main Content -->
        <div class="col-lg-9">
            <!-- Simple Page Header -->
            <div class="simple-main-header mb-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="simple-main-title">
                            {% if request.args.get('category_id') %}
                                {% for cat in categories %}
                                    {% if cat.id == request.args.get('category_id') %}
                                        {{ cat.name }} Products
                                    {% endif %}
                                {% endfor %}
                            {% else %}
                                All Products
                            {% endif %}
                        </h2>
                    </div>
                    <div class="col-md-4">
                        <select class="simple-select" id="sort-products">
                            <option value="default">Sort by: Default</option>
                            <option value="price-low">Price: Low to High</option>
                            <option value="price-high">Price: High to Low</option>
                            <option value="name-asc">Name: A to Z</option>
                            <option value="name-desc">Name: Z to A</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- Products Grid -->
            {% if products and products|length > 0 %}
                <!-- Group products by category -->
                {% set product_categories = {} %}
                {% for product in products %}
                    {% set category_name = product.category.name if product.category else 'Uncategorized' %}
                    {% if category_name not in product_categories %}
                        {% set _ = product_categories.update({category_name: []}) %}
                    {% endif %}
                    {% set _ = product_categories[category_name].append(product) %}
                {% endfor %}
                
                {% for category_name, category_products in product_categories.items() %}
                    <div class="simple-category-section mb-4">
                        <h3 class="simple-category-heading">{{ category_name }}</h3>
                        <div class="row">
                            {% for product in category_products %}
                            <div class="col-md-4 col-sm-6 mb-4">
                                <div class="simple-product-card">
                                    {% if product.is_featured %}
                                    <div class="simple-badge">Featured</div>
                                    {% endif %}
                                    <div class="simple-product-image">
                                        <img src="{{ product.get_main_image() }}"
                                             alt="{{ product.name }}" class="img-fluid">
                                    </div>
                                    <div class="simple-product-details">
                                        <span class="simple-category">{{ product.category.name if product.category else 'Uncategorized' }}</span>
                                        <h4 class="simple-product-title">{{ product.name }}</h4>
                                        <div class="simple-price">${{ "%.2f"|format(product.price) }}</div>
                                        <div class="simple-description">
                                            {{ product.short_description|truncate(60) if product.short_description else product.description|truncate(60) }}
                                        </div>
                                        {% if product.is_in_stock() %}
                                        <form action="{{ url_for('add_to_cart', item_name=product.name) }}" method="POST" class="d-flex align-items-center mt-3">
                                            <input type="number" name="quantity" value="1" min="1" max="{{ product.stock_quantity }}"
                                                   class="simple-quantity-input me-2">
                                            <button type="submit" class="simple-btn-primary flex-grow-1">
                                                Add to Cart
                                            </button>
                                        </form>
                                        {% else %}
                                        <button class="simple-btn-disabled w-100 mt-3" disabled>
                                            Out of Stock
                                        </button>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="simple-no-products">
                    <h3>No Products Found</h3>
                    <p>We couldn't find any products matching your criteria. Try adjusting your filters or browse all categories.</p>
                    <a href="{{ url_for('shop') }}" class="simple-btn-primary">Browse All Products</a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Simple Shop Page Styling */
    .simple-shop-header {
        padding: 1.5rem 0;
        border-bottom: 1px solid #e9ecef;
    }

    .simple-shop-title {
        font-size: 2rem;
        font-weight: 600;
        color: #2E7D32;
        margin-bottom: 0.5rem;
    }

    .simple-shop-subtitle {
        color: #6c757d;
        font-size: 1rem;
        margin: 0;
    }

    .simple-item-count {
        color: #2E7D32;
        font-weight: 500;
        background: #f8f9fa;
        padding: 8px 16px;
        border-radius: 6px;
        border: 1px solid #e9ecef;
    }

    /* Simple Sidebar */
    .simple-sidebar {
        background: #fff;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        overflow: hidden;
    }

    .simple-sidebar-header {
        background: #f8f9fa;
        padding: 1rem;
        border-bottom: 1px solid #e9ecef;
    }

    .simple-sidebar-header h3 {
        margin: 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: #2E7D32;
    }

    .simple-sidebar-body {
        padding: 1rem;
    }

    .simple-category-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .simple-category-item {
        margin-bottom: 0.5rem;
    }

    .simple-category-item a {
        display: block;
        padding: 8px 12px;
        color: #495057;
        text-decoration: none;
        border-radius: 4px;
        transition: all 0.2s ease;
    }

    .simple-category-item a:hover {
        background: #f8f9fa;
        color: #2E7D32;
    }

    .simple-category-item.active a {
        background: #2E7D32;
        color: white;
    }

    /* Simple Form Controls */
    .simple-label {
        color: #495057;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .simple-input {
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 8px 12px;
        width: 100%;
        transition: border-color 0.2s ease;
    }

    .simple-input:focus {
        border-color: #2E7D32;
        outline: none;
        box-shadow: 0 0 0 0.2rem rgba(46, 125, 50, 0.1);
    }

    .simple-select {
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 8px 12px;
        background: white;
        color: #495057;
        width: 100%;
    }

    .simple-select:focus {
        border-color: #2E7D32;
        outline: none;
    }

    /* Simple Main Header */
    .simple-main-header {
        padding: 1rem 0;
        border-bottom: 1px solid #e9ecef;
    }

    .simple-main-title {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 600;
        color: #2E7D32;
    }

    /* Simple Category Section */
    .simple-category-section {
        margin-bottom: 2rem;
    }

    .simple-category-heading {
        font-size: 1.3rem;
        font-weight: 600;
        color: #2E7D32;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #2E7D32;
        display: inline-block;
    }

    /* Simple Product Cards */
    .simple-product-card {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.2s ease;
        height: 100%;
        position: relative;
    }

    .simple-product-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        border-color: #2E7D32;
    }

    .simple-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        background: #2E7D32;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 500;
        z-index: 2;
    }

    .simple-product-image {
        height: 180px;
        overflow: hidden;
    }

    .simple-product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .simple-product-card:hover .simple-product-image img {
        transform: scale(1.05);
    }

    .simple-product-details {
        padding: 1rem;
    }

    .simple-category {
        display: block;
        font-size: 0.8rem;
        color: #6c757d;
        margin-bottom: 0.5rem;
        text-transform: uppercase;
        font-weight: 500;
    }

    .simple-product-title {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #333;
        line-height: 1.3;
    }

    .simple-price {
        font-size: 1.2rem;
        font-weight: 700;
        color: #2E7D32;
        margin-bottom: 0.5rem;
    }

    .simple-description {
        font-size: 0.85rem;
        color: #6c757d;
        margin-bottom: 1rem;
        height: 40px;
        overflow: hidden;
        line-height: 1.4;
    }

    .simple-quantity-input {
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 6px 8px;
        width: 60px;
        text-align: center;
    }

    .simple-btn-primary {
        background: #2E7D32;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 8px 16px;
        font-weight: 500;
        transition: background-color 0.2s ease;
        cursor: pointer;
    }

    .simple-btn-primary:hover {
        background: #1B5E20;
    }

    .simple-btn-disabled {
        background: #6c757d;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 8px 16px;
        font-weight: 500;
        cursor: not-allowed;
    }

    .simple-no-products {
        text-align: center;
        padding: 3rem 2rem;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;
    }

    .simple-no-products h3 {
        color: #2E7D32;
        margin-bottom: 1rem;
    }

    .simple-no-products p {
        color: #6c757d;
        margin-bottom: 1.5rem;
    }
    
    /* Responsive adjustments */
    @media (max-width: 992px) {
        .shop-header {
            flex-direction: column;
            align-items: flex-start;
        }
        
        .shop-sort {
            width: 100%;
            margin-top: 1rem;
        }
    }
    
    @media (max-width: 768px) {
        .product-thumb {
            height: 150px;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Product sorting functionality
        const sortSelect = document.getElementById('sort-products');
        sortSelect.addEventListener('change', function() {
            const sortValue = this.value;
            const productCards = document.querySelectorAll('.product-card');
            const productGrids = document.querySelectorAll('.product-grid');
            
            productGrids.forEach(grid => {
                const products = Array.from(grid.querySelectorAll('.col-md-4'));
                
                products.sort((a, b) => {
                    const aPrice = parseFloat(a.querySelector('.product-price').textContent.replace('$', ''));
                    const bPrice = parseFloat(b.querySelector('.product-price').textContent.replace('$', ''));
                    const aName = a.querySelector('.product-title').textContent;
                    const bName = b.querySelector('.product-title').textContent;
                    
                    if (sortValue === 'price-low') {
                        return aPrice - bPrice;
                    } else if (sortValue === 'price-high') {
                        return bPrice - aPrice;
                    } else if (sortValue === 'name-asc') {
                        return aName.localeCompare(bName);
                    } else if (sortValue === 'name-desc') {
                        return bName.localeCompare(aName);
                    }
                    
                    return 0;
                });
                
                // Clear the grid and append sorted products
                grid.innerHTML = '';
                products.forEach(product => {
                    grid.appendChild(product);
                });
            });
        });
    });
</script>
{% endblock %}


