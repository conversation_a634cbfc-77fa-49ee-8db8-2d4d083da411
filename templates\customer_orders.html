{% extends "base.html" %}
{% block title %}My Orders - Fresh Grocery{% endblock %}

{% block content %}
<div class="container py-5">
    <!-- Page Header -->
    <div class="orders-header mb-5">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2 class="orders-title">
                    <i class="fas fa-shopping-bag"></i> My Orders
                </h2>
                <p class="orders-subtitle">Track and manage your order history</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{{ url_for('shop') }}" class="btn btn-fresh-primary">
                    <i class="fas fa-plus"></i> Place New Order
                </a>
            </div>
        </div>
    </div>

    <!-- Order Statistics -->
    <div class="row mb-5">
        <div class="col-md-3 mb-3">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">{{ order_stats.total_orders }}</div>
                    <div class="stat-label">Total Orders</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">{{ order_stats.pending_orders }}</div>
                    <div class="stat-label">Pending Orders</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">{{ order_stats.delivered_orders }}</div>
                    <div class="stat-label">Delivered</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">${{ "%.0f"|format(order_stats.total_spent) }}</div>
                    <div class="stat-label">Total Spent</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Orders List -->
    <div class="orders-section">
        <div class="section-header mb-4">
            <h4 class="section-title">
                <i class="fas fa-list"></i> Order History
            </h4>
        </div>

        {% if customer_orders and customer_orders|length > 0 %}
            <div class="orders-list">
                {% for order in customer_orders %}
                <div class="order-item">
                    <div class="order-main">
                        <div class="order-info">
                            <div class="order-header">
                                <h5 class="order-number">Order #{{ order.get('order_id', 'N/A')[:8] }}</h5>
                                <span class="order-status-badge 
                                    {% if order.get('status') == 'pending' %}status-pending
                                    {% elif order.get('status') == 'confirmed' %}status-confirmed
                                    {% elif order.get('status') == 'processing' %}status-processing
                                    {% elif order.get('status') == 'shipped' %}status-shipped
                                    {% elif order.get('status') == 'delivered' %}status-delivered
                                    {% elif order.get('status') == 'cancelled' %}status-cancelled
                                    {% else %}status-unknown{% endif %}">
                                    {{ order.get('status', 'Unknown').title() }}
                                </span>
                            </div>
                            <div class="order-meta">
                                <div class="meta-item">
                                    <i class="fas fa-calendar"></i>
                                    <span>{{ order.get('date', 'Unknown')[:10] }}</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-box"></i>
                                    <span>{{ order.get('items')|length if order.get('items') else 0 }} items</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-dollar-sign"></i>
                                    <span>${{ "%.2f"|format(order.get('total', 0)) }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="order-actions">
                            <a href="{{ url_for('track_order', order_id=order.get('order_id')) }}" 
                               class="btn btn-outline-fresh-primary btn-sm">
                                <i class="fas fa-search"></i> Track Order
                            </a>
                            {% if order.get('status') == 'pending' %}
                            <button class="btn btn-outline-danger btn-sm ms-2" 
                                    onclick="cancelOrder('{{ order.get('order_id') }}')">
                                <i class="fas fa-times"></i> Cancel
                            </button>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Order Items Preview -->
                    {% if order.get('items') %}
                    <div class="order-items-preview">
                        <div class="items-grid">
                            {% for item in order.get('items')[:4] %}
                            <div class="item-preview">
                                <div class="item-info">
                                    <span class="item-name">{{ item.get('name', 'Unknown Item') }}</span>
                                    <span class="item-quantity">x{{ item.get('quantity', 1) }}</span>
                                </div>
                                <div class="item-price">${{ "%.2f"|format(item.get('price', 0)) }}</div>
                            </div>
                            {% endfor %}
                            {% if order.get('items')|length > 4 %}
                            <div class="item-preview more-items">
                                <span class="more-text">+{{ order.get('items')|length - 4 }} more items</span>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-orders">
                <div class="empty-icon">
                    <i class="fas fa-shopping-bag"></i>
                </div>
                <h4>No Orders Yet</h4>
                <p class="text-muted">You haven't placed any orders yet. Start shopping to see your orders here!</p>
                <a href="{{ url_for('shop') }}" class="btn btn-fresh-primary">
                    <i class="fas fa-shopping-cart"></i> Start Shopping
                </a>
            </div>
        {% endif %}
    </div>
</div>

<style>
/* Orders Page Styles */
.orders-header {
    background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
    padding: 2rem;
    border-radius: 20px;
    border: 2px solid #e8f5e8;
}

.orders-title {
    color: #2E7D32;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.orders-subtitle {
    color: #666;
    margin-bottom: 0;
}

/* Stat Cards */
.stat-card {
    background: white;
    border: 2px solid #e8f5e8;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(46, 125, 50, 0.1);
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(46, 125, 50, 0.15);
    border-color: #4CAF50;
}

.stat-icon {
    font-size: 2.5rem;
    color: #4CAF50;
    margin-bottom: 1rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #2E7D32;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Order Items */
.orders-list {
    display: grid;
    gap: 1.5rem;
}

.order-item {
    background: white;
    border: 2px solid #e8f5e8;
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(46, 125, 50, 0.1);
}

.order-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(46, 125, 50, 0.15);
    border-color: #4CAF50;
}

.order-main {
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.order-number {
    color: #2E7D32;
    margin-bottom: 0;
    font-weight: 600;
}

.order-meta {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
    font-size: 0.9rem;
}

.meta-item i {
    color: #4CAF50;
    width: 16px;
}

.order-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* Order Items Preview */
.order-items-preview {
    background: #f8f9fa;
    padding: 1rem 1.5rem;
    border-top: 1px solid #e8f5e8;
}

.items-grid {
    display: grid;
    gap: 0.75rem;
}

.item-preview {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
}

.item-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.item-name {
    color: #333;
    font-weight: 500;
}

.item-quantity {
    color: #666;
    font-size: 0.9rem;
}

.item-price {
    color: #2E7D32;
    font-weight: 600;
}

.more-items {
    justify-content: center;
    font-style: italic;
    color: #666;
}

/* Status Badges */
.order-status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Empty State */
.empty-orders {
    text-align: center;
    padding: 4rem 2rem;
    color: #666;
}

.empty-icon {
    font-size: 5rem;
    color: #4CAF50;
    margin-bottom: 1.5rem;
    opacity: 0.5;
}

.empty-orders h4 {
    color: #2E7D32;
    margin-bottom: 1rem;
}

.empty-orders p {
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

@media (max-width: 768px) {
    .order-main {
        flex-direction: column;
        gap: 1rem;
    }
    
    .order-actions {
        flex-direction: row;
        width: 100%;
    }
    
    .order-meta {
        gap: 1rem;
    }
}
</style>

<script>
function cancelOrder(orderId) {
    if (confirm('Are you sure you want to cancel this order?')) {
        // Add cancel order functionality here
        alert('Order cancellation feature will be implemented soon.');
    }
}
</script>
{% endblock %}
