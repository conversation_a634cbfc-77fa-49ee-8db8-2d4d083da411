#!/usr/bin/env python3

import sys
import os

print("Python version:", sys.version)
print("Current directory:", os.getcwd())
print("Python path:", sys.path[:3])

try:
    print("Importing Flask...")
    from flask import Flask
    print("Flask imported successfully")
    
    print("Importing models...")
    from models import db, Product, Category
    print("Models imported successfully")
    
    print("Importing app...")
    from app import app, init_database
    print("App imported successfully")
    
    print("Testing app context...")
    with app.app_context():
        print("App context created successfully")
        print("Database URI:", app.config.get('SQLALCHEMY_DATABASE_URI'))
        
        print("Initializing database...")
        init_database()
        print("Database initialized successfully")
        
        print("Testing database query...")
        product_count = Product.query.count()
        print(f"Products in database: {product_count}")
    
    print("Starting Flask server...")
    app.run(debug=True, host='127.0.0.1', port=5000)
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
