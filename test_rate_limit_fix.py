#!/usr/bin/env python3

"""
Test rate limiting fix
"""

import sys
import os

def test_rate_limit_settings():
    """Test that rate limiting settings are reasonable"""
    print("🧪 Testing Rate Limit Settings...")
    
    try:
        # Read the app.py file to check rate limit settings
        with open('app.py', 'r') as f:
            content = f.read()
        
        # Check for rate limit decorators
        login_rate_limit = None
        register_rate_limit = None
        admin_login_rate_limit = None
        
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if '@rate_limit(' in line and 'def login():' in lines[i+1]:
                login_rate_limit = line.strip()
            elif '@rate_limit(' in line and 'def register():' in lines[i+1]:
                register_rate_limit = line.strip()
            elif '@rate_limit(' in line and 'def admin_login():' in lines[i+1]:
                admin_login_rate_limit = line.strip()
        
        print("📊 Rate Limit Settings Found:")
        print(f"   User Login: {login_rate_limit}")
        print(f"   User Registration: {register_rate_limit}")
        print(f"   Admin Login: {admin_login_rate_limit}")
        
        # Check if settings are reasonable
        issues = []
        
        if login_rate_limit and 'max_requests=5' in login_rate_limit:
            issues.append("User login still has restrictive 5 requests limit")
        elif login_rate_limit and 'max_requests=20' in login_rate_limit:
            print("✅ User login has reasonable 20 requests limit")
        
        if register_rate_limit and 'max_requests=3' in register_rate_limit:
            issues.append("User registration still has restrictive 3 requests limit")
        elif register_rate_limit and 'max_requests=10' in register_rate_limit:
            print("✅ User registration has reasonable 10 requests limit")
        
        if admin_login_rate_limit and 'max_requests=20' in admin_login_rate_limit:
            print("✅ Admin login has reasonable 20 requests limit")
        elif not admin_login_rate_limit:
            print("⚠️ Admin login has no rate limiting (which is fine for development)")
        
        # Check for debug clear function
        if 'debug_clear_rate_limits' in content:
            print("✅ Debug route to clear rate limits is available")
        else:
            issues.append("No debug route to clear rate limits")
        
        if issues:
            print("\n❌ Issues found:")
            for issue in issues:
                print(f"   - {issue}")
            return False
        else:
            print("\n✅ All rate limit settings look good!")
            return True
            
    except Exception as e:
        print(f"❌ Error checking rate limits: {e}")
        return False

def show_rate_limit_info():
    """Show information about rate limits and how to clear them"""
    print("\n📋 Rate Limit Information:")
    print("=" * 50)
    print("Current Settings:")
    print("  • User Login: 20 requests per 15 minutes")
    print("  • User Registration: 10 requests per 60 minutes") 
    print("  • Admin Login: 20 requests per 15 minutes")
    print()
    print("If you still get 'Too many requests' errors:")
    print("  1. Wait 15-60 minutes for limits to reset")
    print("  2. Visit: http://127.0.0.1:5000/debug/clear_rate_limits")
    print("  3. Or restart the Flask server")
    print()
    print("The rate limits are per IP address and reset automatically.")

if __name__ == "__main__":
    print("🚀 Rate Limit Fix Test")
    print("=" * 40)
    
    success = test_rate_limit_settings()
    show_rate_limit_info()
    
    if success:
        print("\n🎉 Rate limiting is properly configured!")
        print("You should now be able to login/logout multiple times without issues.")
    else:
        print("\n💥 Rate limiting configuration needs attention!")
        sys.exit(1)
