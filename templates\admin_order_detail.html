{% extends "base.html" %}
{% block title %}Order Details - {{ order.get('order_id', 'Unknown')[:8] }}...{% endblock %}
{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1>Order Details</h1>
        <p class="text-muted mb-0">Order ID: {{ order.get('order_id', 'Unknown') }}</p>
    </div>
    <div>
        <a href="{{ url_for('admin_dashboard') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
</div>

<!-- Order Status Alert -->
<div class="alert
    {% if order.get('status') == 'pending' %}alert-warning
    {% elif order.get('status') == 'confirmed' %}alert-info
    {% elif order.get('status') == 'delivered' %}alert-success
    {% elif order.get('status') == 'cancelled' %}alert-danger
    {% else %}alert-secondary{% endif %} mb-4">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <strong>Order Status: </strong>
            <span class="badge
                {% if order.get('status') == 'pending' %}bg-warning text-dark
                {% elif order.get('status') == 'confirmed' %}bg-info
                {% elif order.get('status') == 'delivered' %}bg-success
                {% elif order.get('status') == 'cancelled' %}bg-danger
                {% else %}bg-secondary{% endif %}">
                {{ order.get('status', 'Unknown').title() }}
            </span>
        </div>
        <div>
            <strong>Order Total: ${{ "%.2f"|format(order.get('total', 0)) }}</strong>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5>Customer Information</h5>
            </div>
            <div class="card-body">
                <p><strong>Name:</strong> {{ order.get('name', 'Not provided') }}</p>
                <p><strong>Email:</strong> {{ order.get('email', 'Not provided') }}</p>
                <p><strong>Phone:</strong> {{ order.get('phone', 'Not provided') }}</p>
                <p><strong>Address:</strong> {{ order.get('address', 'Not provided') }}</p>
                <p><strong>Order Date:</strong> {{ order.get('date', 'Not provided') }}</p>
                <p><strong>Order ID:</strong> {{ order.get('order_id', 'Not provided') }}</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5>Order Status</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('update_order_status', order_id=order.get('order_id')) }}">
                    <div class="mb-3">
                        <label for="status" class="form-label">Current Status:</label>
                        <select class="form-select" id="status" name="status">
                            <option value="pending" {% if order.get('status') == 'pending' %}selected{% endif %}>Pending</option>
                            <option value="confirmed" {% if order.get('status') == 'confirmed' %}selected{% endif %}>Confirmed</option>
                            <option value="delivered" {% if order.get('status') == 'delivered' %}selected{% endif %}>Delivered</option>
                            <option value="cancelled" {% if order.get('status') == 'cancelled' %}selected{% endif %}>Cancelled</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary">Update Status</button>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Order Items</h5>
        <span class="badge bg-primary">
            {% if order.get('items') and order.get('items')|length > 0 %}
                {{ order.get('items')|length }} item{{ 's' if order.get('items')|length != 1 else '' }}
            {% else %}
                No items
            {% endif %}
        </span>
    </div>
    <div class="card-body">
        {% if order.get('items') and order.get('items')|length > 0 %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>Item Name</th>
                        <th>Category</th>
                        <th>Unit Price</th>
                        <th>Quantity</th>
                        <th>Subtotal</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in order.get('items') %}
                    <tr>
                        <td>
                            <strong>{{ item.get('name', 'Unknown Item') }}</strong>
                        </td>
                        <td>
                            <span class="badge bg-light text-dark">{{ item.get('category', 'Uncategorized') }}</span>
                        </td>
                        <td>
                            <span class="text-success">${{ "%.2f"|format(item.get('price', 0)) }}</span>
                        </td>
                        <td>
                            <span class="badge bg-primary">{{ item.get('quantity', 0) }}</span>
                        </td>
                        <td>
                            <strong class="text-success">${{ "%.2f"|format(item.get('price', 0) * item.get('quantity', 0)) }}</strong>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot class="table-dark">
                    <tr>
                        <td colspan="4" class="text-end"><strong>Order Total:</strong></td>
                        <td><strong>${{ "%.2f"|format(order.get('total', 0)) }}</strong></td>
                    </tr>
                </tfoot>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No Items in This Order</h5>
            <p class="text-muted">This order appears to be empty or the items data is missing.</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Order Summary Card -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card border-success">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0"><i class="fas fa-chart-line"></i> Order Summary</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <p class="mb-1"><strong>Items Count:</strong></p>
                        <h4 class="text-primary">{{ order.get('items')|length if order.get('items') else 0 }}</h4>
                    </div>
                    <div class="col-6">
                        <p class="mb-1"><strong>Total Amount:</strong></p>
                        <h4 class="text-success">${{ "%.2f"|format(order.get('total', 0)) }}</h4>
                    </div>
                </div>
                <hr>
                <p class="mb-1"><strong>Average Item Price:</strong></p>
                {% if order.get('items') and order.get('items')|length > 0 %}
                    <span class="text-info">${{ "%.2f"|format(order.get('total', 0) / order.get('items')|length) }}</span>
                {% else %}
                    <span class="text-muted">N/A</span>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0"><i class="fas fa-clock"></i> Order Timeline</h6>
            </div>
            <div class="card-body">
                <p class="mb-1"><strong>Order Placed:</strong></p>
                <p class="text-muted">{{ order.get('date', 'Unknown') }}</p>
                <p class="mb-1"><strong>Current Status:</strong></p>
                <span class="badge
                    {% if order.get('status') == 'pending' %}bg-warning text-dark
                    {% elif order.get('status') == 'confirmed' %}bg-info
                    {% elif order.get('status') == 'delivered' %}bg-success
                    {% elif order.get('status') == 'cancelled' %}bg-danger
                    {% else %}bg-secondary{% endif %}">
                    {{ order.get('status', 'Unknown').title() }}
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Order Status History -->
{% if order.get('status_history') %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0"><i class="fas fa-history"></i> Order Status History</h6>
            </div>
            <div class="card-body">
                <div class="timeline">
                    {% for change in order.get('status_history') %}
                    <div class="timeline-item mb-3">
                        <div class="row">
                            <div class="col-md-3">
                                <small class="text-muted">{{ change.get('changed_at', 'Unknown') }}</small>
                            </div>
                            <div class="col-md-6">
                                <span class="badge bg-secondary me-2">{{ change.get('from_status', 'Unknown').title() }}</span>
                                <i class="fas fa-arrow-right text-muted mx-2"></i>
                                <span class="badge
                                    {% if change.get('to_status') == 'pending' %}bg-warning text-dark
                                    {% elif change.get('to_status') == 'confirmed' %}bg-info
                                    {% elif change.get('to_status') == 'shipped' %}bg-primary
                                    {% elif change.get('to_status') == 'delivered' %}bg-success
                                    {% elif change.get('to_status') == 'cancelled' %}bg-danger
                                    {% else %}bg-secondary{% endif %}">
                                    {{ change.get('to_status', 'Unknown').title() }}
                                </span>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">by {{ change.get('changed_by', 'System') }}</small>
                                {% if change.get('notes') %}
                                <br><small class="text-info">{{ change.get('notes') }}</small>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Enhanced Status Update Form -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0"><i class="fas fa-edit"></i> Update Order Status</h6>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('update_order_status', order_id=order.get('order_id')) }}">
                    <div class="mb-3">
                        <label for="status" class="form-label">New Status:</label>
                        <select name="status" id="status" class="form-select" required>
                            <option value="">Select Status...</option>
                            <option value="pending" {% if order.get('status') == 'pending' %}selected{% endif %}>Pending</option>
                            <option value="confirmed" {% if order.get('status') == 'confirmed' %}selected{% endif %}>Confirmed</option>
                            <option value="processing" {% if order.get('status') == 'processing' %}selected{% endif %}>Processing</option>
                            <option value="shipped" {% if order.get('status') == 'shipped' %}selected{% endif %}>Shipped</option>
                            <option value="delivered" {% if order.get('status') == 'delivered' %}selected{% endif %}>Delivered</option>
                            <option value="cancelled" {% if order.get('status') == 'cancelled' %}selected{% endif %}>Cancelled</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes (Optional):</label>
                        <textarea name="notes" id="notes" class="form-control" rows="2" placeholder="Add any notes about this status change..."></textarea>
                    </div>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save"></i> Update Status
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card border-secondary">
            <div class="card-header bg-secondary text-white">
                <h6 class="mb-0"><i class="fas fa-info-circle"></i> Order Processing Info</h6>
            </div>
            <div class="card-body">
                {% if order.get('confirmed_at') %}
                <p><strong>Confirmed:</strong> {{ order.get('confirmed_at') }}</p>
                {% endif %}
                {% if order.get('processing_at') %}
                <p><strong>Processing Started:</strong> {{ order.get('processing_at') }}</p>
                {% endif %}
                {% if order.get('shipped_at') %}
                <p><strong>Shipped:</strong> {{ order.get('shipped_at') }}</p>
                {% endif %}
                {% if order.get('delivered_at') %}
                <p><strong>Delivered:</strong> {{ order.get('delivered_at') }}</p>
                {% endif %}
                {% if order.get('processing_time_hours') %}
                <p><strong>Processing Time:</strong> {{ order.get('processing_time_hours') }} hours</p>
                {% endif %}

                {% if not order.get('confirmed_at') and not order.get('shipped_at') and not order.get('delivered_at') %}
                <p class="text-muted">No processing timestamps recorded yet.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
</div>
{% endblock %}