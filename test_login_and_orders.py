#!/usr/bin/env python3

import os
import sys
import json
import requests

def test_login_and_orders():
    """Test login and order tracking functionality"""
    
    session = requests.Session()
    
    print("=== TESTING LOGIN AND ORDER TRACKING ===\n")
    
    # Step 1: Get login page to get CSRF token if needed
    try:
        login_page = session.get('http://localhost:5000/login')
        print(f"✅ Login page loaded: {login_page.status_code}")
    except Exception as e:
        print(f"❌ Error loading login page: {e}")
        return
    
    # Step 2: Attempt login
    login_data = {
        'email': '<EMAIL>',
        'password': 'password123'
    }
    
    try:
        login_response = session.post('http://localhost:5000/login', data=login_data)
        print(f"✅ Login attempt: {login_response.status_code}")
        
        if login_response.status_code == 302:
            print("✅ Login successful (redirected)")
        elif "Invalid" in login_response.text:
            print("❌ Login failed - invalid credentials")
            return
        else:
            print("❓ Login status unclear")
            
    except Exception as e:
        print(f"❌ Error during login: {e}")
        return
    
    # Step 3: Test profile page
    try:
        profile_response = session.get('http://localhost:5000/profile')
        print(f"\n✅ Profile page: {profile_response.status_code}")
        
        if "<EMAIL>" in profile_response.text:
            print("✅ Correct user logged in")
        else:
            print("❌ Wrong user or not logged in")
            
        # Check for order statistics
        if "Total Orders" in profile_response.text:
            print("✅ Order statistics found")
        else:
            print("❌ No order statistics")
            
        # Check for recent orders
        if "Recent Orders" in profile_response.text:
            print("✅ Recent orders section found")
        else:
            print("❌ No recent orders section")
            
    except Exception as e:
        print(f"❌ Error testing profile: {e}")
    
    # Step 4: Test My Orders page
    try:
        orders_response = session.get('http://localhost:5000/my-orders')
        print(f"\n✅ My Orders page: {orders_response.status_code}")
        
        if "Order History" in orders_response.text:
            print("✅ Order history page loaded")
        else:
            print("❌ Order history page not loaded")
            
        if "No orders found" in orders_response.text:
            print("⚠️ No orders found message")
        elif "Order #" in orders_response.text:
            print("✅ Orders are displayed")
            
            # Count orders
            order_count = orders_response.text.count("Order #")
            print(f"✅ Found {order_count} orders displayed")
        else:
            print("❓ Unknown order status")
            
    except Exception as e:
        print(f"❌ Error testing my-orders: {e}")
    
    # Step 5: Test individual order tracking
    try:
        track_response = session.get('http://localhost:5000/track-order/c689745c')
        print(f"\n✅ Order tracking page: {track_response.status_code}")
        
        if "Order Details" in track_response.text:
            print("✅ Order tracking page loaded")
        else:
            print("❌ Order tracking page not loaded")
            
    except Exception as e:
        print(f"❌ Error testing order tracking: {e}")

if __name__ == "__main__":
    test_login_and_orders()
