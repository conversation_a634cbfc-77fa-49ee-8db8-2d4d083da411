/* Fresh Grocery Store - Enhanced Styling */

/* ===== SMOOTH SCROLLING ENHANCEMENTS ===== */
* {
    scroll-behavior: smooth;
}

html {
    scroll-behavior: smooth;
    scroll-padding-top: 100px; /* Account for navbar and spacing */
}

/* Enhanced General Styles with Fresh Theme */
body {
    font-family: 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    background: linear-gradient(135deg, #f8f9fa 0%, #e8f5e8 100%);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden; /* Prevent horizontal scroll */
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
    color: var(--fresh-primary-dark);
    font-weight: 600;
}

/* Fresh Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--soft-cream);
}

::-webkit-scrollbar-thumb {
    background: var(--fresh-gradient);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--fresh-primary);
}

/* Enhanced Fresh Navbar */
.navbar-brand {
    font-weight: 700;
    font-size: 1.6rem;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    transform: scale(1.05);
}

/* Fresh Alert Styles */
.alert {
    border: none;
    border-radius: var(--border-radius-md);
    border-left: 4px solid;
}

.alert-success {
    background: var(--leaf-gradient);
    border-left-color: var(--fresh-primary);
    color: var(--fresh-primary-dark);
}

.alert-info {
    background: linear-gradient(135deg, var(--sky-blue) 0%, rgba(3, 169, 244, 0.1) 100%);
    border-left-color: var(--sky-blue);
    color: var(--text-primary);
}

.alert-warning {
    background: linear-gradient(135deg, var(--sunshine-yellow) 0%, rgba(255, 193, 7, 0.1) 100%);
    border-left-color: var(--sunshine-yellow);
    color: var(--text-primary);
}

.alert-danger {
    background: linear-gradient(135deg, #f44336 0%, rgba(244, 67, 54, 0.1) 100%);
    border-left-color: #f44336;
    color: var(--text-primary);
}

/* Enhanced Fresh Card Effects */
.card {
    transition: all 0.3s ease;
    border: 2px solid var(--fresh-mint);
    border-radius: var(--border-radius-lg);
    background: var(--pure-white);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--nature-gradient);
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: var(--organic-shadow);
    border-color: var(--fresh-primary);
}

/* Fresh Form Controls */
.form-control {
    border: 2px solid var(--fresh-mint);
    border-radius: var(--border-radius-sm);
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--fresh-primary);
    box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
}

.form-select {
    border: 2px solid var(--fresh-mint);
    border-radius: var(--border-radius-sm);
    transition: all 0.3s ease;
}

.form-select:focus {
    border-color: var(--fresh-primary);
    box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
}

/* Enhanced Fresh Product Image Styling */
.card-img-top {
    transition: transform 0.5s ease;
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
}

.card:hover .card-img-top {
    transform: scale(1.1);
}

/* Fresh Button Enhancements */
.btn {
    border-radius: var(--border-radius-md);
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--fresh-gradient);
    border: none;
    color: var(--text-on-green);
}

.btn-primary:hover {
    background: var(--fresh-primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--fresh-shadow-hover);
}

.btn-success {
    background: var(--nature-gradient);
    border: none;
}

.btn-success:hover {
    background: var(--fresh-primary);
    transform: translateY(-2px);
}

.btn-outline-light {
    border: 2px solid rgba(255, 255, 255, 0.8);
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
}

.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: white;
    color: white;
    transform: translateY(-1px);
}

/* Enhanced Fresh Footer Styling */
footer {
    margin-top: 3rem;
    position: relative;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--nature-gradient);
}

footer a {
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

footer a:hover {
    color: rgba(255, 255, 255, 1) !important;
    transform: translateY(-1px);
}

footer a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--sunshine-yellow);
    transition: width 0.3s ease;
}

footer a:hover::after {
    width: 100%;
}

/* Fresh Badge Styles */
.badge {
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    padding: 6px 12px;
}

.bg-danger {
    background: linear-gradient(135deg, #f44336, #d32f2f) !important;
}

.bg-success {
    background: var(--fresh-gradient) !important;
}

.bg-warning {
    background: linear-gradient(135deg, var(--sunshine-yellow), #f57c00) !important;
    color: var(--text-primary) !important;
}

.bg-info {
    background: linear-gradient(135deg, var(--sky-blue), #0288d1) !important;
}

/* Admin dashboard cards */
.admin-card {
    border-radius: 10px;
    overflow: hidden;
}

/* Custom button styles */
.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* Form styling */
.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Table styling */
.table-responsive {
    overflow-x: auto;
}

/* Jumbotron styling */
.jumbotron {
    background-color: #e9ecef;
    border-radius: 0.3rem;
    padding: 2rem 1rem;
}

/* Badge styling */
.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
}

/* Alert styling */
.alert {
    border-radius: 0.25rem;
    padding: 1rem;
}

/* Product Card Styles */
.product-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid rgba(0,0,0,0.125);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.price-tag {
    font-weight: bold;
    color: #28a745;
    font-size: 1.2rem;
}

/* Featured Products Section */
.featured-product-section {
    background-color: #f8f9fa;
    padding: 3rem 0;
}

/* Media queries for responsive design */
@media (max-width: 768px) {
    .jumbotron {
        padding: 1.5rem;
    }
    
    .display-4 {
        font-size: 2.5rem;
    }
}
