#!/usr/bin/env python3

import os
import sys
import json

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from models import User

def fix_order_tracking():
    """Fix and verify the order tracking system"""
    
    with app.app_context():
        print("=== FIXING ORDER TRACKING SYSTEM ===\n")
        
        # Step 1: Verify users
        print("1. CHECKING USERS:")
        users = User.query.all()
        for user in users:
            print(f"   ✅ {user.email} - {user.first_name} {user.last_name}")
        
        # Step 2: Check orders data
        print(f"\n2. CHECKING ORDERS:")
        try:
            from app import load_orders
            orders = load_orders()
            print(f"   ✅ Total orders loaded: {len(orders)}")
            
            # Group orders by email
            orders_by_email = {}
            for order in orders:
                email = order.get('email', 'NO_EMAIL')
                if email not in orders_by_email:
                    orders_by_email[email] = []
                orders_by_email[email].append(order)
            
            print(f"   📊 Orders by email:")
            for email, user_orders in orders_by_email.items():
                print(f"      {email}: {len(user_orders)} orders")
                
        except Exception as e:
            print(f"   ❌ Error loading orders: {e}")
            return
        
        # Step 3: Test order filtering for each user
        print(f"\n3. TESTING ORDER FILTERING:")
        for user in users:
            customer_orders = []
            for order in orders:
                if order.get('email') == user.email:
                    customer_orders.append(order)
            
            print(f"   {user.email}: {len(customer_orders)} orders")
            if customer_orders:
                for order in customer_orders[:2]:  # Show first 2 orders
                    print(f"      - Order #{order.get('order_id', 'N/A')[:8]} - {order.get('status', 'No status')} - ${order.get('total', 0)}")
        
        # Step 4: Check for data integrity issues
        print(f"\n4. CHECKING DATA INTEGRITY:")
        issues_found = []
        
        for i, order in enumerate(orders):
            order_id = order.get('order_id', f'ORDER_{i}')
            
            # Check required fields
            if not order.get('email'):
                issues_found.append(f"Order {order_id}: Missing email")
            if not order.get('date'):
                issues_found.append(f"Order {order_id}: Missing date")
            if order.get('total') is None:
                issues_found.append(f"Order {order_id}: Missing total")
            if not order.get('items'):
                issues_found.append(f"Order {order_id}: No items")
        
        if issues_found:
            print(f"   ⚠️ Issues found:")
            for issue in issues_found[:5]:  # Show first 5 issues
                print(f"      - {issue}")
        else:
            print(f"   ✅ No data integrity issues found")
        
        # Step 5: Test template data structure
        print(f"\n5. TESTING TEMPLATE DATA STRUCTURE:")
        test_user = User.query.filter_by(email='<EMAIL>').first()
        if test_user:
            customer_orders = [o for o in orders if o.get('email') == test_user.email]
            
            # Calculate order statistics (same as in the route)
            order_stats = {
                'total_orders': len(customer_orders),
                'pending_orders': len([o for o in customer_orders if o.get('status') == 'pending']),
                'confirmed_orders': len([o for o in customer_orders if o.get('status') == 'confirmed']),
                'shipped_orders': len([o for o in customer_orders if o.get('status') == 'shipped']),
                'delivered_orders': len([o for o in customer_orders if o.get('status') == 'delivered']),
                'cancelled_orders': len([o for o in customer_orders if o.get('status') == 'cancelled']),
                'total_spent': sum(float(o.get('total', 0)) for o in customer_orders if o.get('status') != 'cancelled'),
                'average_order_value': 0
            }
            
            if order_stats['total_orders'] > 0:
                order_stats['average_order_value'] = order_stats['total_spent'] / order_stats['total_orders']
            
            print(f"   ✅ Template data for {test_user.email}:")
            for key, value in order_stats.items():
                print(f"      {key}: {value}")
        
        # Step 6: Generate fix recommendations
        print(f"\n6. RECOMMENDATIONS:")
        
        # <NAME_EMAIL> has orders
        test_orders = [o for o in orders if o.get('email') == '<EMAIL>']
        if test_orders:
            print(f"   ✅ User '<EMAIL>' has {len(test_orders)} orders")
            print(f"   📝 To see orders, login with:")
            print(f"      Email: <EMAIL>")
            print(f"      Password: password123")
        else:
            print(f"   ⚠️ No orders found for '<EMAIL>'")
        
        # Check if any user has orders
        users_with_orders = set(o.get('email') for o in orders if o.get('email'))
        users_in_db = set(u.email for u in users)
        
        print(f"   📊 Users with orders: {users_with_orders}")
        print(f"   📊 Users in database: {users_in_db}")
        
        missing_users = users_with_orders - users_in_db
        if missing_users:
            print(f"   ⚠️ Orders exist for users not in database: {missing_users}")
        
        print(f"\n🎉 ORDER TRACKING SYSTEM ANALYSIS COMPLETE!")
        
        return {
            'total_orders': len(orders),
            'total_users': len(users),
            'orders_by_email': orders_by_email,
            'issues_found': issues_found,
            'test_user_orders': len(test_orders) if test_orders else 0
        }

if __name__ == "__main__":
    result = fix_order_tracking()
    print(f"\n📋 SUMMARY:")
    print(f"   Total Orders: {result['total_orders']}")
    print(f"   Total Users: {result['total_users']}")
    print(f"   Test User Orders: {result['test_user_orders']}")
    print(f"   Issues Found: {len(result['issues_found'])}")
