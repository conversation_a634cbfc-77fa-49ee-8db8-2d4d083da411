#!/usr/bin/env python3

"""
Minimal test to check cart functionality without running full server
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("🔍 Testing Cart Logic Directly...")
    
    # Import the app and models
    from app import app, db
    from models import Product, Category
    
    print("✓ Successfully imported app and models")
    
    # Test within app context
    with app.app_context():
        print("✓ App context created")
        
        # Simulate a cart session
        print("\n🛒 Simulating Cart Session...")
        
        # Create a mock session cart
        mock_cart = []
        
        # Test adding "Organic Bananas" (database product)
        product = Product.query.filter_by(name="Organic Bananas", is_active=True).first()
        if product:
            cart_item = {
                'name': "Organic Bananas",
                'price': float(product.price),
                'quantity': 2,
                'image': product.get_main_image()
            }
            mock_cart.append(cart_item)
            print(f"✓ Added database product: {cart_item}")
        
        # Test adding "Apple" (JSON product)
        from app import load_grocery_data
        json_items = load_grocery_data()
        apple_item = next((i for i in json_items if i['name'] == 'Apple'), None)
        if apple_item:
            cart_item = {
                'name': 'Apple',
                'price': apple_item['price'],
                'quantity': 1,
                'image': apple_item.get('image', 'default.jpg')
            }
            mock_cart.append(cart_item)
            print(f"✓ Added JSON product: {cart_item}")
        
        # Calculate total
        total = sum(item.get('price', 0) * item.get('quantity', 0) for item in mock_cart)
        print(f"\n💰 Cart Summary:")
        print(f"  - Items: {len(mock_cart)}")
        print(f"  - Total: ${total:.2f}")
        
        # Test cart display logic
        print(f"\n📄 Cart Display Test:")
        if mock_cart and len(mock_cart) > 0:
            print("✓ Cart has items - should display cart table")
            for i, item in enumerate(mock_cart):
                print(f"  Item {i+1}: {item['name']} x {item['quantity']} = ${item['price'] * item['quantity']:.2f}")
                
                # Test image path logic
                image_path = item['image']
                if image_path.startswith('/static/'):
                    print(f"    Image: {image_path} (full path)")
                else:
                    print(f"    Image: /static/img/{image_path} (relative path)")
        else:
            print("❌ Cart is empty - would show 'Your cart is empty' message")
        
        print(f"\n✅ Cart logic test completed successfully!")
        print(f"The cart functionality should work properly.")
        
        # Test template conditions
        print(f"\n🎨 Template Condition Tests:")
        print(f"  - cart exists: {mock_cart is not None}")
        print(f"  - cart length > 0: {len(mock_cart) > 0}")
        print(f"  - cart and cart|length > 0: {mock_cart and len(mock_cart) > 0}")
        
        if mock_cart and len(mock_cart) > 0:
            print("✅ Template should show cart items")
        else:
            print("❌ Template would show empty cart message")

except Exception as e:
    print(f"❌ Error during testing: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
