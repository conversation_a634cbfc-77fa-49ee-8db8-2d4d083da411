#!/usr/bin/env python3

import os
import sys

print("Testing database initialization...")

try:
    # Import Flask and create a simple app
    from flask import Flask
    
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///test_grocery.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    print("Flask app created successfully")
    
    # Import models
    from models import db, User, Category, Product
    
    print("Models imported successfully")
    
    # Initialize database
    db.init_app(app)
    
    print("Database initialized with app")
    
    with app.app_context():
        print("Creating tables...")
        db.create_all()
        print("Tables created successfully")
        
        # Test creating a category
        test_cat = Category(name='Test', description='Test category', is_active=True, sort_order=1)
        db.session.add(test_cat)
        db.session.commit()
        print("Test category created")
        
        # Test querying
        categories = Category.query.all()
        print(f"Categories in database: {len(categories)}")
        
        print("Database test completed successfully!")
        
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
