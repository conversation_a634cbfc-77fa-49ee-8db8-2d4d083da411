{% extends "base.html" %}
{% block title %}UPI Payment{% endblock %}

{% block extra_css %}
<style>
    .payment-container {
        max-width: 500px;
        margin: 0 auto;
    }
    
    .payment-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        text-align: center;
    }
    
    .payment-header {
        margin-bottom: 30px;
    }
    
    .payment-amount {
        font-size: 2.5rem;
        font-weight: bold;
        color: #28a745;
        margin: 20px 0;
    }
    
    .qr-code-container {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 30px;
        margin: 30px 0;
        border: 2px dashed #dee2e6;
    }
    
    .qr-placeholder {
        width: 200px;
        height: 200px;
        background: white;
        border: 2px solid #dee2e6;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        font-size: 3rem;
        color: #6c757d;
    }
    
    .upi-id-display {
        background: #e9ecef;
        border-radius: 8px;
        padding: 15px;
        margin: 20px 0;
        font-family: monospace;
        font-size: 1.1rem;
        word-break: break-all;
    }
    
    .payment-methods {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
        gap: 15px;
        margin: 30px 0;
    }
    
    .payment-app {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        padding: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        color: inherit;
    }
    
    .payment-app:hover {
        border-color: #007bff;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,123,255,0.15);
        text-decoration: none;
        color: inherit;
    }
    
    .payment-app-icon {
        font-size: 2.5rem;
        margin-bottom: 8px;
        display: block;
    }
    
    .payment-app-name {
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .payment-status {
        margin: 30px 0;
        padding: 20px;
        border-radius: 8px;
        background: #fff3cd;
        border: 1px solid #ffeaa7;
    }
    
    .payment-instructions {
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
        text-align: left;
    }
    
    .btn-verify {
        background: linear-gradient(135deg, #28a745, #20c997);
        border: none;
        padding: 12px 30px;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: 8px;
        margin: 10px;
    }
    
    .btn-verify:hover {
        background: linear-gradient(135deg, #218838, #1ea085);
        transform: translateY(-1px);
    }
    
    .timer-display {
        font-size: 1.2rem;
        font-weight: bold;
        color: #dc3545;
        margin: 20px 0;
    }
    
    .loading-spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #007bff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="payment-container">
        <div class="payment-card">
            <div class="payment-header">
                <h2><i class="fas fa-qrcode text-primary"></i> UPI Payment</h2>
                <p class="text-muted">Pay using {{ payment_provider.title() }} or any UPI app</p>
            </div>
            
            <div class="payment-amount">₹{{ "%.2f"|format(amount) }}</div>
            <p><strong>Order ID:</strong> {{ order_id }}</p>
            <p><strong>Payment ID:</strong> {{ payment_id }}</p>
            
            <!-- QR Code Section -->
            <div class="qr-code-container">
                <!-- 🎯 ADD YOUR QR CODE HERE -->
                <!-- Option 1: Static QR Code Image -->
                <div class="qr-code-image" style="display: none;">
                    <img src="{{ url_for('static', filename='images/QR code.jpg') }}"
                         alt="UPI QR Code"
                         style="width: 200px; height: 200px; border-radius: 8px;">
                </div>

                <!-- Option 2: Dynamic QR Code (Generated) -->
                <div class="qr-code-dynamic" style="display: none;">
                    <img src="{{ url_for('generate_qr_code', payment_id=payment_id, amount=amount) }}"
                         alt="Dynamic UPI QR Code"
                         style="width: 200px; height: 200px; border-radius: 8px;">
                </div>

                <!-- Option 3: QR Code using QR API Service -->
                <div class="qr-code-api">
                    <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=upi://pay?pa=sksajarulhoque@okicici%26pn=Grocery%20Store%26am={{ amount }}%26cu=INR%26tn=Order%20{{ order_id }}"
                         alt="UPI QR Code"
                         style="width: 200px; height: 200px; border-radius: 8px; border: 2px solid #dee2e6;">
                </div>

                <!-- Fallback placeholder (remove when you add real QR) -->
                <div class="qr-placeholder" style="display: none;">
                    <i class="fas fa-qrcode"></i>
                </div>

                <p><strong>Scan QR Code</strong></p>
                <p class="text-muted">Open any UPI app and scan this QR code to pay</p>

                <!-- QR Code Instructions -->
                <div class="qr-instructions mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        <strong>QR Code contains:</strong><br>
                        UPI ID: sksajarulhoque@okicici<br>
                        Amount: ₹{{ "%.2f"|format(amount) }}<br>
                        Order: {{ order_id }}
                    </small>
                </div>
            </div>
            
            <!-- UPI ID Display -->
            <div class="upi-id-display">
                <strong>UPI ID:</strong> sksajarulhoque@okicici
                <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyUpiId()">
                    <i class="fas fa-copy"></i> Copy
                </button>
            </div>
            
            <!-- Payment Apps -->
            <div class="payment-methods">
                <a href="#" class="payment-app" onclick="openUpiApp('gpay')">
                    <i class="fab fa-google-pay payment-app-icon" style="color: #4285f4;"></i>
                    <div class="payment-app-name">GPay</div>
                </a>
                <a href="#" class="payment-app" onclick="openUpiApp('paytm')">
                    <i class="fas fa-mobile-alt payment-app-icon" style="color: #00baf2;"></i>
                    <div class="payment-app-name">Paytm</div>
                </a>
                <a href="#" class="payment-app" onclick="openUpiApp('phonepe')">
                    <i class="fas fa-phone payment-app-icon" style="color: #5f259f;"></i>
                    <div class="payment-app-name">PhonePe</div>
                </a>
                <a href="#" class="payment-app" onclick="openUpiApp('bhim')">
                    <i class="fas fa-university payment-app-icon" style="color: #ff6b35;"></i>
                    <div class="payment-app-name">BHIM</div>
                </a>
            </div>
            
            <!-- Payment Instructions -->
            <div class="payment-instructions">
                <h6><i class="fas fa-info-circle"></i> How to Pay:</h6>
                <ol class="mb-0">
                    <li>Open your UPI app (GPay, Paytm, PhonePe, etc.)</li>
                    <li>Scan the QR code above OR use UPI ID: <strong>sksajarulhoque@okicici</strong></li>
                    <li>Enter amount: <strong>₹{{ "%.2f"|format(amount) }}</strong></li>
                    <li>Complete the payment</li>
                    <li>Click "Verify Payment" below</li>
                </ol>
            </div>
            
            <!-- Payment Status -->
            <div class="payment-status">
                <div id="paymentStatus">
                    <i class="fas fa-clock text-warning"></i>
                    <strong>Waiting for payment...</strong>
                    <div class="timer-display" id="timer">Time remaining: 10:00</div>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="d-flex justify-content-center flex-wrap">
                <button class="btn btn-success btn-verify" onclick="verifyPayment()">
                    <i class="fas fa-check-circle"></i> Verify Payment
                </button>
                <button class="btn btn-outline-secondary btn-verify" onclick="cancelPayment()">
                    <i class="fas fa-times"></i> Cancel
                </button>
            </div>
            
            <!-- Auto-refresh notice -->
            <div class="mt-3">
                <small class="text-muted">
                    <i class="fas fa-sync-alt"></i> 
                    This page will automatically check for payment confirmation every 10 seconds
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let timeLeft = 600; // 10 minutes in seconds
let timerInterval;
let checkPaymentInterval;

document.addEventListener('DOMContentLoaded', function() {
    startTimer();
    startPaymentCheck();
});

function startTimer() {
    timerInterval = setInterval(function() {
        timeLeft--;
        updateTimerDisplay();
        
        if (timeLeft <= 0) {
            clearInterval(timerInterval);
            clearInterval(checkPaymentInterval);
            showTimeoutMessage();
        }
    }, 1000);
}

function updateTimerDisplay() {
    const minutes = Math.floor(timeLeft / 60);
    const seconds = timeLeft % 60;
    const display = `${minutes}:${seconds.toString().padStart(2, '0')}`;
    document.getElementById('timer').innerHTML = `Time remaining: ${display}`;
}

function startPaymentCheck() {
    // Check payment status every 10 seconds
    checkPaymentInterval = setInterval(function() {
        checkPaymentStatus();
    }, 10000);
}

function checkPaymentStatus() {
    fetch(`/check-payment-status/{{ payment_id }}`)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'completed') {
                clearInterval(timerInterval);
                clearInterval(checkPaymentInterval);
                showPaymentSuccess();
            } else if (data.status === 'failed') {
                clearInterval(timerInterval);
                clearInterval(checkPaymentInterval);
                showPaymentFailed();
            }
        })
        .catch(error => {
            console.log('Payment check error:', error);
        });
}

function verifyPayment() {
    const button = event.target;
    const originalText = button.innerHTML;
    
    button.innerHTML = '<span class="loading-spinner"></span> Verifying...';
    button.disabled = true;
    
    fetch(`/verify-payment/{{ payment_id }}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showPaymentSuccess();
        } else {
            alert(data.message || 'Payment verification failed. Please try again.');
            button.innerHTML = originalText;
            button.disabled = false;
        }
    })
    .catch(error => {
        alert('Error verifying payment. Please try again.');
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function openUpiApp(app) {
    const upiString = `upi://pay?pa=sksajarulhoque@okicici&pn=Grocery Store&am={{ amount }}&cu=INR&tn=Order {{ order_id }}`;

    // Try to open the specific app
    let appUrl = upiString;

    switch(app) {
        case 'gpay':
            appUrl = `gpay://upi/pay?pa=sksajarulhoque@okicici&pn=Grocery Store&am={{ amount }}&cu=INR&tn=Order {{ order_id }}`;
            break;
        case 'paytm':
            appUrl = `paytm://pay?pa=sksajarulhoque@okicici&pn=Grocery Store&am={{ amount }}&cu=INR&tn=Order {{ order_id }}`;
            break;
        case 'phonepe':
            appUrl = `phonepe://pay?pa=sksajarulhoque@okicici&pn=Grocery Store&am={{ amount }}&cu=INR&tn=Order {{ order_id }}`;
            break;
    }
    
    window.location.href = appUrl;
    
    // Fallback to generic UPI if app-specific doesn't work
    setTimeout(() => {
        window.location.href = upiString;
    }, 1000);
}

function copyUpiId() {
    navigator.clipboard.writeText('sksajarulhoque@okicici').then(function() {
        alert('UPI ID copied to clipboard!');
    });
}

function cancelPayment() {
    if (confirm('Are you sure you want to cancel this payment?')) {
        window.location.href = '/payment-selection';
    }
}

function showPaymentSuccess() {
    document.getElementById('paymentStatus').innerHTML = `
        <i class="fas fa-check-circle text-success"></i>
        <strong style="color: #28a745;">Payment Successful!</strong>
        <div class="mt-2">Redirecting to confirmation page...</div>
    `;
    
    setTimeout(() => {
        window.location.href = `/payment-success/{{ payment_id }}`;
    }, 2000);
}

function showPaymentFailed() {
    document.getElementById('paymentStatus').innerHTML = `
        <i class="fas fa-times-circle text-danger"></i>
        <strong style="color: #dc3545;">Payment Failed!</strong>
        <div class="mt-2">Please try again or choose a different payment method.</div>
    `;
}

function showTimeoutMessage() {
    document.getElementById('paymentStatus').innerHTML = `
        <i class="fas fa-clock text-warning"></i>
        <strong style="color: #ffc107;">Payment Timeout!</strong>
        <div class="mt-2">Please start a new payment process.</div>
    `;
}
</script>
{% endblock %}
