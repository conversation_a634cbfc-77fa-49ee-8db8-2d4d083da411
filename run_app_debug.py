#!/usr/bin/env python3

"""
Debug version of app startup to identify hanging issues
"""

import sys
import traceback

def run_app():
    try:
        print("🔍 Starting app debug...")
        
        print("1. Importing modules...")
        import os
        import json
        from datetime import datetime
        print("   ✅ Basic imports successful")
        
        print("2. Importing Flask...")
        from flask import Flask, render_template, request, redirect, url_for, session, flash
        print("   ✅ Flask imports successful")
        
        print("3. Importing database modules...")
        from flask_sqlalchemy import SQLAlchemy
        from flask_migrate import Migrate
        print("   ✅ Database imports successful")
        
        print("4. Creating Flask app...")
        app = Flask(__name__)
        app.secret_key = 'debug_key_for_testing'
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///grocery_store.db'
        app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
        print("   ✅ Flask app created")
        
        print("5. Initializing database...")
        db = SQLAlchemy(app)
        migrate = Migrate(app, db)
        print("   ✅ Database initialized")
        
        print("6. Importing models...")
        sys.path.insert(0, '.')
        from models import Product, Category, User
        print("   ✅ Models imported")
        
        print("7. Creating simple routes...")
        
        @app.route('/')
        def home():
            return "<h1>🏠 Home</h1><p>App is running!</p><a href='/test_cart'>Test Cart</a>"
        
        @app.route('/test_cart')
        def test_cart():
            cart = session.get('cart', [])
            return f"""
            <h1>🛒 Cart Test</h1>
            <p>Cart items: {len(cart)}</p>
            <p>Cart data: {cart}</p>
            <form action="/add_test_item" method="post">
                <button type="submit">Add Test Item</button>
            </form>
            <a href="/">Home</a>
            """
        
        @app.route('/add_test_item', methods=['POST'])
        def add_test_item():
            cart = session.get('cart', [])
            cart.append({
                'name': 'Test Item',
                'price': 1.99,
                'quantity': 1
            })
            session['cart'] = cart
            session.permanent = True
            return redirect(url_for('test_cart'))
        
        print("   ✅ Routes created")
        
        print("8. Starting server...")
        app.run(debug=True, host='127.0.0.1', port=5001, use_reloader=False)
        
    except Exception as e:
        print(f"❌ Error during startup: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Debug App Startup")
    print("=" * 40)
    run_app()
