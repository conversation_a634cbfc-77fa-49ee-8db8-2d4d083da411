<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Grocery Store{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/fresh-theme.css') }}">
    <style>
        /* Fix dropdown z-index issues */
        .navbar .dropdown-menu {
            z-index: 1050 !important;
        }
        .navbar {
            z-index: 1000 !important;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark navbar-fresh" style="position: relative; z-index: 1000;">
        <div class="container">
            <a class="navbar-brand leaf-float" href="{{ url_for('home') }}">
                <i class="fas fa-leaf"></i> Fresh Grocery
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('home') }}">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('shop') }}">Shop</a>
                    </li>
                    {% if request.endpoint == 'home' %}
                    <li class="nav-item">
                        <a class="nav-link" href="#featured-products">Featured</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#categories">Categories</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#why-choose-us">Why Us</a>
                    </li>
                    {% endif %}
                </ul>
                <div class="d-flex align-items-center">
                    <a href="{{ url_for('view_cart') }}" class="btn btn-fresh-secondary me-2">
                        <i class="fas fa-shopping-cart"></i> Cart
                        {% if session.cart and session.cart|length > 0 %}
                        <span class="badge" style="background: var(--sunshine-yellow); color: var(--text-primary);">{{ session.cart|length }}</span>
                        {% endif %}
                    </a>

                    {% if current_user.is_authenticated %}
                        <!-- User is logged in -->
                        <div class="dropdown me-2">
                            <button class="btn btn-fresh-secondary dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user"></i> {{ current_user.first_name }}
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="{{ url_for('profile') }}"><i class="fas fa-user-circle"></i> My Profile</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('my_orders') }}"><i class="fas fa-shopping-bag"></i> My Orders</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('edit_profile') }}"><i class="fas fa-edit"></i> Edit Profile</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('change_password') }}"><i class="fas fa-key"></i> Change Password</a></li>
                                <li><hr class="dropdown-divider"></li>
                                {% if current_user.is_staff() %}
                                <li><a class="dropdown-item" href="{{ url_for('admin_login') }}"><i class="fas fa-user-shield"></i> Admin Panel</a></li>
                                <li><hr class="dropdown-divider"></li>
                                {% endif %}
                                <li><a class="dropdown-item" href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                            </ul>
                        </div>
                    {% else %}
                        <!-- User is not logged in - Single Login Button with Dropdown -->
                        <div class="dropdown">
                            <button class="btn btn-fresh-secondary dropdown-toggle" type="button" id="loginDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-sign-in-alt"></i> Login
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="loginDropdown">
                                <li><a class="dropdown-item" href="{{ url_for('login') }}"><i class="fas fa-sign-in-alt"></i> Sign In</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('register') }}"><i class="fas fa-user-plus"></i> Create Account</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('admin_login') }}"><i class="fas fa-user-shield"></i> Admin Login</a></li>
                            </ul>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main class="fade-in">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="py-5 mt-5" style="background: var(--fresh-gradient); color: var(--text-on-green);">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5><i class="fas fa-leaf"></i> Fresh Grocery Store</h5>
                    <p>Your one-stop shop for fresh, organic groceries and household essentials. Bringing nature's best to your table.</p>
                    <div class="d-flex gap-3 mt-3">
                        <i class="fab fa-facebook-f" style="font-size: 1.2rem; opacity: 0.8; cursor: pointer;"></i>
                        <i class="fab fa-twitter" style="font-size: 1.2rem; opacity: 0.8; cursor: pointer;"></i>
                        <i class="fab fa-instagram" style="font-size: 1.2rem; opacity: 0.8; cursor: pointer;"></i>
                    </div>
                </div>
                <div class="col-md-4">
                    <h5><i class="fas fa-seedling"></i> Quick Links</h5>
                    <ul class="list-unstyled">
                        <li><a href="{{ url_for('home') }}" style="color: rgba(255,255,255,0.9); text-decoration: none;"><i class="fas fa-home"></i> Home</a></li>
                        <li><a href="{{ url_for('shop') }}" style="color: rgba(255,255,255,0.9); text-decoration: none;"><i class="fas fa-store"></i> Shop</a></li>
                        <li><a href="{{ url_for('view_cart') }}" style="color: rgba(255,255,255,0.9); text-decoration: none;"><i class="fas fa-shopping-cart"></i> Cart</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5><i class="fas fa-map-marker-alt"></i> Contact Us</h5>
                    <address style="opacity: 0.9;">
                        <p><i class="fas fa-store"></i> 123 Fresh Market Street, Green Valley</p>
                        <p><i class="fas fa-phone"></i> (555) FRESH-99</p>
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr style="border-color: rgba(255,255,255,0.2); margin: 2rem 0 1rem;">
            <div class="text-center">
                <p style="opacity: 0.8; margin: 0;">🌱 &copy; 2025 Fresh Grocery Store. Naturally fresh, organically yours. 🌱</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
