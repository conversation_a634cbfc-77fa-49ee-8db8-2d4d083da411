{% extends "base.html" %}
{% block title %}Select Payment Method{% endblock %}

{% block extra_css %}
<style>
    .payment-container {
        max-width: 600px;
        margin: 0 auto;
    }
    
    .payment-method-card {
        border: 2px solid #e9ecef;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
    }
    
    .payment-method-card:hover {
        border-color: #28a745;
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.15);
        transform: translateY(-2px);
    }
    
    .payment-method-card.selected {
        border-color: #28a745;
        background: #f8fff9;
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);
    }
    
    .payment-method-icon {
        font-size: 2.5rem;
        margin-right: 15px;
        width: 60px;
        text-align: center;
    }
    
    .payment-method-details h5 {
        margin: 0 0 5px 0;
        color: #333;
        font-weight: 600;
    }
    
    .payment-method-details p {
        margin: 0;
        color: #666;
        font-size: 0.9rem;
    }
    
    .order-summary {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 30px;
    }
    
    .order-summary h4 {
        color: #28a745;
        margin-bottom: 15px;
    }
    
    .amount-display {
        font-size: 1.8rem;
        font-weight: bold;
        color: #28a745;
        text-align: center;
        margin: 20px 0;
    }
    
    .upi-apps-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
        margin-top: 15px;
    }
    
    .upi-app-card {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
    }
    
    .upi-app-card:hover {
        border-color: #007bff;
        transform: translateY(-2px);
    }
    
    .upi-app-card.selected {
        border-color: #007bff;
        background: #f0f8ff;
    }
    
    .upi-app-icon {
        font-size: 2rem;
        margin-bottom: 8px;
        display: block;
    }
    
    .upi-app-name {
        font-size: 0.85rem;
        font-weight: 600;
        color: #333;
    }
    
    .payment-form {
        background: white;
        border-radius: 12px;
        padding: 25px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .btn-pay {
        background: linear-gradient(135deg, #28a745, #20c997);
        border: none;
        padding: 15px 30px;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: 8px;
        width: 100%;
        margin-top: 20px;
    }
    
    .btn-pay:hover {
        background: linear-gradient(135deg, #218838, #1ea085);
        transform: translateY(-1px);
    }
    
    .security-info {
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 8px;
        padding: 15px;
        margin-top: 20px;
        font-size: 0.9rem;
    }
    
    .cod-info {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 15px;
        margin-top: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="payment-container">
        <!-- Order Summary -->
        <div class="order-summary">
            <h4><i class="fas fa-shopping-cart"></i> Order Summary</h4>
            <div class="row">
                <div class="col-6">
                    <strong>Order ID:</strong><br>
                    <small class="text-muted">{{ order.get('order_id', '') }}</small>
                </div>
                <div class="col-6 text-end">
                    <strong>Items:</strong><br>
                    <small class="text-muted">{{ order.get('items', [])|length }} items</small>
                </div>
            </div>
            <div class="amount-display">
                ₹{{ "%.2f"|format(order.get('total', 0)) }}
            </div>
        </div>

        <!-- Payment Form -->
        <div class="payment-form">
            <h3 class="mb-4"><i class="fas fa-credit-card"></i> Select Payment Method</h3>
            
            <form id="paymentForm" method="POST" action="{{ url_for('process_payment') }}">
                <input type="hidden" name="order_id" value="{{ order.get('order_id', '') }}">
                <input type="hidden" name="amount" value="{{ order.get('total', 0) }}">
                <input type="hidden" id="selectedPaymentMethod" name="payment_method" value="">
                <input type="hidden" id="selectedProvider" name="payment_provider" value="">
                
                <!-- UPI Payment Methods -->
                <div class="payment-method-card" data-method="upi" data-provider="upi">
                    <div class="d-flex align-items-center">
                        <div class="payment-method-icon">
                            <i class="fas fa-qrcode text-primary"></i>
                        </div>
                        <div class="payment-method-details flex-grow-1">
                            <h5>UPI Payment</h5>
                            <p>Pay using Google Pay, Paytm, PhonePe, or any UPI app</p>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="payment_type" value="upi" id="upi">
                        </div>
                    </div>
                    
                    <!-- UPI Apps Selection -->
                    <div class="upi-apps-section" style="display: none;">
                        <div class="upi-apps-grid">
                            {% for method in payment_methods %}
                                {% if method.method_type == 'upi' %}
                                <div class="upi-app-card" data-provider="{{ method.provider_name }}">
                                    <i class="{{ method.icon_class }} upi-app-icon"></i>
                                    <div class="upi-app-name">{{ method.display_name }}</div>
                                </div>
                                {% endif %}
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- Cash on Delivery -->
                <div class="payment-method-card" data-method="cod" data-provider="cod">
                    <div class="d-flex align-items-center">
                        <div class="payment-method-icon">
                            <i class="fas fa-money-bill-wave text-success"></i>
                        </div>
                        <div class="payment-method-details flex-grow-1">
                            <h5>Cash on Delivery</h5>
                            <p>Pay when your order is delivered to your doorstep</p>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="payment_type" value="cod" id="cod">
                        </div>
                    </div>
                    
                    <div class="cod-info" style="display: none;">
                        <i class="fas fa-info-circle text-warning"></i>
                        <strong>COD Information:</strong>
                        <ul class="mb-0 mt-2">
                            <li>Minimum order: ₹50</li>
                            <li>Maximum order: ₹5,000</li>
                            <li>Please keep exact change ready</li>
                            <li>COD charges: Free</li>
                        </ul>
                    </div>
                </div>

                <!-- Payment Button -->
                <button type="submit" class="btn btn-success btn-pay" id="payButton" disabled>
                    <i class="fas fa-lock"></i> Select Payment Method
                </button>
            </form>

            <!-- Security Information -->
            <div class="security-info">
                <i class="fas fa-shield-alt text-primary"></i>
                <strong>Secure Payment:</strong> Your payment information is encrypted and secure. We never store your payment details.
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentCards = document.querySelectorAll('.payment-method-card');
    const upiAppsSection = document.querySelector('.upi-apps-section');
    const codInfo = document.querySelector('.cod-info');
    const upiAppCards = document.querySelectorAll('.upi-app-card');
    const payButton = document.getElementById('payButton');
    const selectedMethodInput = document.getElementById('selectedPaymentMethod');
    const selectedProviderInput = document.getElementById('selectedProvider');
    
    let selectedMethod = '';
    let selectedProvider = '';
    
    // Payment method selection
    paymentCards.forEach(card => {
        card.addEventListener('click', function() {
            // Remove previous selections
            paymentCards.forEach(c => c.classList.remove('selected'));
            upiAppsSection.style.display = 'none';
            codInfo.style.display = 'none';
            
            // Select current card
            this.classList.add('selected');
            const method = this.dataset.method;
            const provider = this.dataset.provider;
            
            // Update radio button
            const radio = this.querySelector('input[type="radio"]');
            radio.checked = true;
            
            selectedMethod = method;
            selectedProvider = provider;
            
            // Show relevant sections
            if (method === 'upi') {
                upiAppsSection.style.display = 'block';
                updatePayButton('Select UPI App');
            } else if (method === 'cod') {
                codInfo.style.display = 'block';
                selectedProvider = 'cod';
                updatePayButton('Place Order (COD)');
                enablePayButton();
            }
            
            updateHiddenInputs();
        });
    });
    
    // UPI app selection
    upiAppCards.forEach(card => {
        card.addEventListener('click', function() {
            upiAppCards.forEach(c => c.classList.remove('selected'));
            this.classList.add('selected');
            
            selectedProvider = this.dataset.provider;
            updatePayButton(`Pay with ${this.querySelector('.upi-app-name').textContent}`);
            enablePayButton();
            updateHiddenInputs();
        });
    });
    
    function updatePayButton(text) {
        payButton.innerHTML = `<i class="fas fa-lock"></i> ${text}`;
    }
    
    function enablePayButton() {
        payButton.disabled = false;
        payButton.classList.remove('btn-secondary');
        payButton.classList.add('btn-success');
    }
    
    function updateHiddenInputs() {
        selectedMethodInput.value = selectedMethod;
        selectedProviderInput.value = selectedProvider;
    }
    
    // Form submission
    document.getElementById('paymentForm').addEventListener('submit', function(e) {
        if (!selectedMethod || !selectedProvider) {
            e.preventDefault();
            alert('Please select a payment method');
            return false;
        }
        
        // Show loading state
        payButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        payButton.disabled = true;
    });
});
</script>
{% endblock %}
