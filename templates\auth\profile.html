{% extends "base.html" %}
{% block title %}My Profile - Fresh Grocery{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <!-- Profile Sidebar -->
        <div class="col-md-4 mb-4">
            <div class="profile-sidebar">
                <div class="profile-header">
                    <div class="profile-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <h3 class="profile-name">{{ user.get_full_name() }}</h3>
                    <p class="profile-username">@{{ user.username }}</p>
                    <span class="profile-badge">{{ user.role.title() }}</span>
                </div>
                
                <div class="profile-stats">
                    <div class="stat-item">
                        <div class="stat-number">{{ order_stats.total_orders if order_stats else 0 }}</div>
                        <div class="stat-label">Total Orders</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{ order_stats.pending_orders if order_stats else 0 }}</div>
                        <div class="stat-label">Pending</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${{ "%.0f"|format(order_stats.total_spent) if order_stats else 0 }}</div>
                        <div class="stat-label">Total Spent</div>
                    </div>
                </div>
                
                <div class="profile-actions">
                    <a href="{{ url_for('my_orders') }}" class="btn profile-btn-success w-100 mb-2">
                        <i class="fas fa-shopping-bag"></i> My Orders
                    </a>
                    <a href="{{ url_for('edit_profile') }}" class="btn profile-btn-primary w-100 mb-2">
                        <i class="fas fa-edit"></i> Edit Profile
                    </a>
                    <a href="{{ url_for('change_password') }}" class="btn profile-btn-secondary w-100 mb-2">
                        <i class="fas fa-key"></i> Change Password
                    </a>
                    <a href="{{ url_for('logout') }}" class="btn profile-btn-danger w-100">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Profile Content -->
        <div class="col-md-8">
            <!-- Personal Information -->
            <div class="profile-section mb-4">
                <div class="section-header">
                    <h4 class="section-title">
                        <i class="fas fa-user-circle"></i> Personal Information
                    </h4>
                </div>
                <div class="section-content">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">First Name</label>
                                <div class="info-value">{{ user.first_name }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">Last Name</label>
                                <div class="info-value">{{ user.last_name }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">Email</label>
                                <div class="info-value">{{ user.email }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">Phone</label>
                                <div class="info-value">{{ user.phone if user.phone else 'Not provided' }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Address Information -->
            <div class="profile-section mb-4">
                <div class="section-header">
                    <h4 class="section-title">
                        <i class="fas fa-map-marker-alt"></i> Address Information
                    </h4>
                </div>
                <div class="section-content">
                    <div class="row">
                        <div class="col-12">
                            <div class="info-item">
                                <label class="info-label">Address</label>
                                <div class="info-value">{{ user.address if user.address else 'Not provided' }}</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-item">
                                <label class="info-label">City</label>
                                <div class="info-value">{{ user.city if user.city else 'Not provided' }}</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-item">
                                <label class="info-label">State</label>
                                <div class="info-value">{{ user.state if user.state else 'Not provided' }}</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-item">
                                <label class="info-label">ZIP Code</label>
                                <div class="info-value">{{ user.zip_code if user.zip_code else 'Not provided' }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Account Information -->
            <div class="profile-section mb-4">
                <div class="section-header">
                    <h4 class="section-title">
                        <i class="fas fa-cog"></i> Account Information
                    </h4>
                </div>
                <div class="section-content">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">Member Since</label>
                                <div class="info-value">{{ user.created_at.strftime('%B %d, %Y') if user.created_at else 'Unknown' }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">Last Login</label>
                                <div class="info-value">{{ user.last_login.strftime('%B %d, %Y at %I:%M %p') if user.last_login else 'Never' }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">Account Status</label>
                                <div class="info-value">
                                    {% if user.is_active %}
                                        <span class="status-badge status-active">Active</span>
                                    {% else %}
                                        <span class="status-badge status-inactive">Inactive</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">Email Verified</label>
                                <div class="info-value">
                                    {% if user.is_verified %}
                                        <span class="status-badge status-verified">Verified</span>
                                    {% else %}
                                        <span class="status-badge status-unverified">Not Verified</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Orders Section -->
            <div class="profile-section mb-4">
                <div class="section-header">
                    <h4 class="section-title">
                        <i class="fas fa-shopping-bag"></i> Recent Orders
                    </h4>
                    <a href="{{ url_for('my_orders') }}" class="btn btn-sm profile-btn-primary">
                        <i class="fas fa-eye"></i> View All Orders
                    </a>
                </div>
                <div class="section-content">
                    {% if customer_orders and customer_orders|length > 0 %}
                        <div class="orders-grid">
                            {% for order in customer_orders[:3] %}
                            <div class="order-card">
                                <div class="order-header">
                                    <div class="order-id">
                                        <strong>Order #{{ order.get('order_id', 'N/A')[:8] }}</strong>
                                    </div>
                                    <div class="order-status">
                                        <span class="status-badge
                                            {% if order.get('status') == 'pending' %}status-pending
                                            {% elif order.get('status') == 'confirmed' %}status-confirmed
                                            {% elif order.get('status') == 'shipped' %}status-shipped
                                            {% elif order.get('status') == 'delivered' %}status-delivered
                                            {% elif order.get('status') == 'cancelled' %}status-cancelled
                                            {% else %}status-unknown{% endif %}">
                                            {{ order.get('status', 'Unknown').title() }}
                                        </span>
                                    </div>
                                </div>
                                <div class="order-details">
                                    <div class="order-date">
                                        <i class="fas fa-calendar"></i> {{ order.get('date', 'Unknown')[:10] }}
                                    </div>
                                    <div class="order-total">
                                        <i class="fas fa-dollar-sign"></i> ${{ "%.2f"|format(order.get('total', 0)) }}
                                    </div>
                                    <div class="order-items">
                                        <i class="fas fa-box"></i> {{ order.get('items')|length if order.get('items') else 0 }} items
                                    </div>
                                </div>
                                <div class="order-actions">
                                    <a href="{{ url_for('track_order', order_id=order.get('order_id')) }}" class="btn btn-sm profile-btn-success">
                                        <i class="fas fa-search"></i> Track Order
                                    </a>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-state">
                            <div class="empty-icon">
                                <i class="fas fa-shopping-bag"></i>
                            </div>
                            <h5>No Orders Yet</h5>
                            <p class="text-muted">You haven't placed any orders yet. Start shopping to see your orders here!</p>
                            <a href="{{ url_for('shop') }}" class="btn profile-btn-primary">
                                <i class="fas fa-shopping-cart"></i> Start Shopping
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Profile Styles */
.profile-sidebar {
    background: white;
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(46, 125, 50, 0.15);
    border: 2px solid #e8f5e8;
    overflow: hidden;
}

.profile-header {
    background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
    padding: 2rem;
    text-align: center;
    border-bottom: 1px solid #e8f5e8;
}

.profile-avatar {
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    box-shadow: 0 8px 25px rgba(46, 125, 50, 0.3);
}

.profile-avatar i {
    font-size: 3rem;
    color: white;
}

.profile-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1B5E20;
    margin-bottom: 0.5rem;
}

.profile-username {
    color: #2E7D32;
    font-size: 1rem;
    margin-bottom: 1rem;
}

.profile-badge {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    color: white;
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.profile-stats {
    display: flex;
    justify-content: space-around;
    padding: 1.5rem;
    border-bottom: 1px solid #e8f5e8;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2E7D32;
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
}

.profile-actions {
    padding: 1.5rem;
}

.profile-btn-primary {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    border: none;
    border-radius: 10px;
    padding: 10px 20px;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
}

.profile-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(46, 125, 50, 0.4);
    color: white;
}

.profile-btn-secondary {
    background: transparent;
    border: 2px solid #4CAF50;
    border-radius: 10px;
    padding: 8px 20px;
    font-weight: 600;
    color: #2E7D32;
    transition: all 0.3s ease;
}

.profile-btn-secondary:hover {
    background: #4CAF50;
    color: white;
    transform: translateY(-2px);
}

.profile-btn-danger {
    background: transparent;
    border: 2px solid #dc3545;
    border-radius: 10px;
    padding: 8px 20px;
    font-weight: 600;
    color: #dc3545;
    transition: all 0.3s ease;
}

.profile-btn-danger:hover {
    background: #dc3545;
    color: white;
    transform: translateY(-2px);
}

.profile-section {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(46, 125, 50, 0.1);
    border: 2px solid #e8f5e8;
    overflow: hidden;
}

.section-header {
    background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e8f5e8;
}

.section-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1B5E20;
    margin: 0;
}

.section-content {
    padding: 1.5rem;
}

.info-item {
    margin-bottom: 1rem;
}

.info-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #2E7D32;
    margin-bottom: 0.25rem;
    display: block;
}

.info-value {
    font-size: 1rem;
    color: #333;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-active {
    background: #d4edda;
    color: #155724;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
}

.status-verified {
    background: #d1ecf1;
    color: #0c5460;
}

.status-unverified {
    background: #fff3cd;
    color: #856404;
}

.profile-btn-success {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.2);
}

.profile-btn-success:hover {
    background: linear-gradient(135deg, #388E3C, #1B5E20);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
    color: white;
}

/* Order Cards */
.orders-grid {
    display: grid;
    gap: 1rem;
}

.order-card {
    background: white;
    border: 2px solid #e8f5e8;
    border-radius: 15px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(46, 125, 50, 0.1);
}

.order-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(46, 125, 50, 0.15);
    border-color: #4CAF50;
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #e8f5e8;
}

.order-id {
    font-size: 1.1rem;
    color: #2E7D32;
}

.order-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.order-details > div {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
    font-size: 0.9rem;
}

.order-details i {
    color: #4CAF50;
    width: 16px;
}

.order-actions {
    text-align: right;
}

/* Status Badges for Orders */
.status-pending {
    background: linear-gradient(135deg, #FFC107, #FF8F00);
    color: #333;
}

.status-confirmed {
    background: linear-gradient(135deg, #2196F3, #1565C0);
    color: white;
}

.status-shipped {
    background: linear-gradient(135deg, #9C27B0, #6A1B9A);
    color: white;
}

.status-delivered {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    color: white;
}

.status-cancelled {
    background: linear-gradient(135deg, #F44336, #C62828);
    color: white;
}

.status-unknown {
    background: linear-gradient(135deg, #9E9E9E, #616161);
    color: white;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #666;
}

.empty-icon {
    font-size: 4rem;
    color: #4CAF50;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h5 {
    color: #2E7D32;
    margin-bottom: 0.5rem;
}

.empty-state p {
    margin-bottom: 1.5rem;
}
</style>
{% endblock %}
