#!/usr/bin/env python3

"""
Test script to verify the cart functionality fix
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("Testing cart functionality fix...")
    
    # Import the app and models
    from app import app, db
    from models import Product, Category
    
    print("✓ Successfully imported app and models")
    
    # Test within app context
    with app.app_context():
        print("✓ App context created")
        
        # Check if database tables exist
        try:
            product_count = Product.query.count()
            category_count = Category.query.count()
            print(f"✓ Database accessible: {product_count} products, {category_count} categories")
        except Exception as e:
            print(f"✗ Database error: {e}")
            sys.exit(1)
        
        # List all products
        products = Product.query.filter_by(is_active=True).all()
        print(f"\n📦 Available Products ({len(products)}):")
        for product in products:
            print(f"  - {product.name} (${product.price}) - Stock: {product.stock_quantity}")
        
        # Test the add_to_cart logic manually
        print(f"\n🛒 Testing cart logic...")
        
        # Test 1: Try to find "Organic Bananas" in database
        organic_bananas = Product.query.filter_by(name="Organic Bananas", is_active=True).first()
        if organic_bananas:
            print(f"✓ Found 'Organic Bananas' in database: ${organic_bananas.price}, Stock: {organic_bananas.stock_quantity}")
        else:
            print("✗ 'Organic Bananas' not found in database")
        
        # Test 2: Try to find "Apple" in database
        apple = Product.query.filter_by(name="Apple", is_active=True).first()
        if apple:
            print(f"✓ Found 'Apple' in database: ${apple.price}, Stock: {apple.stock_quantity}")
        else:
            print("✗ 'Apple' not found in database - will fallback to JSON")
        
        # Test 3: Check JSON fallback
        from app import load_grocery_data
        json_items = load_grocery_data()
        print(f"\n📄 JSON fallback items ({len(json_items)}):")
        for item in json_items[:5]:  # Show first 5
            print(f"  - {item['name']} (${item['price']}) - Stock: {item['quantity']}")
        
        print(f"\n✅ Cart fix test completed successfully!")
        print(f"The updated add_to_cart function will:")
        print(f"  1. First try to find products in the database")
        print(f"  2. If not found, fallback to JSON file")
        print(f"  3. This should resolve the 'Organic Bananas not found' error")

except Exception as e:
    print(f"✗ Error during testing: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
