{% extends "base.html" %}
{% block title %}Admin Dashboard{% endblock %}
{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-tachometer-alt"></i> Admin Dashboard</h2>
        <a href="{{ url_for('admin_logout') }}" class="btn btn-danger">
            <i class="fas fa-sign-out-alt"></i> Logout
        </a>
    </div>

    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <h5 class="card-title">Pending Orders</h5>
                    <p class="card-text display-4">{{ pending_count|default(0) }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h5 class="card-title">Confirmed Orders</h5>
                    <p class="card-text display-4">{{ confirmed_count|default(0) }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h5 class="card-title">Delivered Orders</h5>
                    <p class="card-text display-4">{{ delivered_count|default(0) }}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3>Recent Orders</h3>
                    <div>
                        <a href="{{ url_for('order_analytics') }}" class="btn btn-info me-2">
                            <i class="fas fa-chart-bar"></i> Analytics
                        </a>
                        <a href="{{ url_for('admin_inventory') }}" class="btn btn-primary">
                            <i class="fas fa-boxes"></i> Manage Inventory
                        </a>
                        {% if config.DEBUG %}
                        <a href="{{ url_for('debug_fix_orders') }}" class="btn btn-warning ms-2">
                            <i class="fas fa-wrench"></i> Fix Orders
                        </a>
                        {% endif %}
                    </div>
                </div>

                <!-- Bulk Operations Panel -->
                <div class="card-body">
                    <form id="bulkOperationsForm" method="POST" action="{{ url_for('bulk_order_operations') }}">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <select name="bulk_action" class="form-select" required>
                                    <option value="">Select Bulk Action...</option>
                                    <option value="mark_confirmed">Mark as Confirmed</option>
                                    <option value="mark_shipped">Mark as Shipped</option>
                                    <option value="mark_delivered">Mark as Delivered</option>
                                    <option value="cancel_orders">Cancel Orders</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-warning" onclick="return confirmBulkAction()">
                                    <i class="fas fa-tasks"></i> Apply to Selected Orders
                                </button>
                                <button type="button" class="btn btn-secondary ms-2" onclick="selectAllOrders()">
                                    <i class="fas fa-check-square"></i> Select All
                                </button>
                                <button type="button" class="btn btn-outline-secondary ms-1" onclick="clearSelection()">
                                    <i class="fas fa-square"></i> Clear All
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="card-body">
                    {% if orders and orders|length > 0 %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" id="selectAllCheckbox" onchange="toggleAllOrders(this)">
                                    </th>
                                    <th>Order ID</th>
                                    <th>Date</th>
                                    <th>Customer</th>
                                    <th>Items</th>
                                    <th>Total</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in orders %}
                                <tr>
                                    <td>
                                        <input type="checkbox" name="selected_orders" value="{{ order.get('order_id') }}"
                                               class="order-checkbox" form="bulkOperationsForm">
                                    </td>
                                    <td>{{ order.get('order_id', 'N/A')[:8] if order.get('order_id') else 'N/A' }}...</td>
                                    <td>{{ order.get('date', 'N/A') }}</td>
                                    <td>{{ order.get('name', 'Not provided') }}</td>
                                    <td>
                                        {% if order.get('items') and order.get('items') is iterable %}
                                            {{ order.get('items')|length }} items
                                        {% else %}
                                            0 items
                                        {% endif %}
                                    </td>
                                    <td>${{ "%.2f"|format(order.get('total', 0)) }}</td>
                                    <td>
                                        {% if order.get('status') == 'pending' %}
                                        <span class="badge bg-warning">Pending</span>
                                        {% elif order.get('status') == 'confirmed' %}
                                        <span class="badge bg-info">Confirmed</span>
                                        {% elif order.get('status') == 'delivered' %}
                                        <span class="badge bg-success">Delivered</span>
                                        {% elif order.get('status') == 'cancelled' %}
                                        <span class="badge bg-danger">Cancelled</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ order.get('status', 'Unknown') }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('admin_order_detail', order_id=order.get('order_id')) }}" class="btn btn-sm btn-info me-1">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                        <form method="POST" action="{{ url_for('update_order_status', order_id=order.get('order_id')) }}" class="d-inline">
                                            <select name="status" class="form-select form-select-sm d-inline-block" style="width: auto;">
                                                <option value="pending" {% if order.get('status') == 'pending' %}selected{% endif %}>Pending</option>
                                                <option value="confirmed" {% if order.get('status') == 'confirmed' %}selected{% endif %}>Confirmed</option>
                                                <option value="delivered" {% if order.get('status') == 'delivered' %}selected{% endif %}>Delivered</option>
                                                <option value="cancelled" {% if order.get('status') == 'cancelled' %}selected{% endif %}>Cancelled</option>
                                            </select>
                                            <button type="submit" class="btn btn-sm btn-success ms-1">Update</button>
                                        </form>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">No orders found.</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleAllOrders(selectAllCheckbox) {
    const checkboxes = document.querySelectorAll('.order-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
}

function selectAllOrders() {
    const checkboxes = document.querySelectorAll('.order-checkbox');
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    selectAllCheckbox.checked = true;
}

function clearSelection() {
    const checkboxes = document.querySelectorAll('.order-checkbox');
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    selectAllCheckbox.checked = false;
}

function confirmBulkAction() {
    const selectedOrders = document.querySelectorAll('.order-checkbox:checked');
    const action = document.querySelector('select[name="bulk_action"]').value;

    if (selectedOrders.length === 0) {
        alert('Please select at least one order.');
        return false;
    }

    if (!action) {
        alert('Please select an action.');
        return false;
    }

    const actionText = action.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
    return confirm(`Are you sure you want to ${actionText} for ${selectedOrders.length} selected order(s)?`);
}

// Auto-update select all checkbox based on individual selections
document.addEventListener('DOMContentLoaded', function() {
    const orderCheckboxes = document.querySelectorAll('.order-checkbox');
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');

    orderCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const checkedBoxes = document.querySelectorAll('.order-checkbox:checked');
            selectAllCheckbox.checked = checkedBoxes.length === orderCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < orderCheckboxes.length;
        });
    });
});
</script>
{% endblock %}

