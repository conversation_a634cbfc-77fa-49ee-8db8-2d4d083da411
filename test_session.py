#!/usr/bin/env python3

"""
Test script to check session and cart functionality
"""

import requests
import sys

def test_cart_session():
    """Test cart functionality with session tracking"""
    
    print("🧪 Testing Cart Session Functionality...")
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    try:
        # Step 1: Get the shop page
        print("\n1️⃣ Loading shop page...")
        shop_response = session.get('http://127.0.0.1:5000/shop')
        if shop_response.status_code == 200:
            print("✅ Shop page loaded successfully")
        else:
            print(f"❌ Shop page failed: {shop_response.status_code}")
            return
        
        # Step 2: Add an item to cart
        print("\n2️⃣ Adding 'Organic Bananas' to cart...")
        add_response = session.post('http://127.0.0.1:5000/add_to_cart/Organic Bananas', 
                                   data={'quantity': 2})
        
        if add_response.status_code == 200:
            print("✅ Add to cart request successful")
            # Check if there's a success message in the response
            if "Added" in add_response.text or "cart" in add_response.text.lower():
                print("✅ Success message found in response")
        else:
            print(f"❌ Add to cart failed: {add_response.status_code}")
            return
        
        # Step 3: Check cart page
        print("\n3️⃣ Checking cart page...")
        cart_response = session.get('http://127.0.0.1:5000/cart')
        
        if cart_response.status_code == 200:
            print("✅ Cart page loaded successfully")
            
            # Check if cart has items
            cart_text = cart_response.text
            if "Organic Bananas" in cart_text:
                print("✅ 'Organic Bananas' found in cart page!")
            else:
                print("❌ 'Organic Bananas' NOT found in cart page")
                
            if "$0.00" in cart_text:
                print("❌ Cart shows $0.00 total (empty cart)")
            else:
                print("✅ Cart shows non-zero total")
                
            # Look for cart items table
            if "Cart Items" in cart_text:
                print("✅ Cart items section found")
            else:
                print("❌ Cart items section not found")
                
            if "Your cart is empty" in cart_text:
                print("❌ Cart shows as empty")
            else:
                print("✅ Cart does not show as empty")
                
        else:
            print(f"❌ Cart page failed: {cart_response.status_code}")
            return
        
        # Step 4: Try adding a JSON item
        print("\n4️⃣ Adding 'Apple' (JSON item) to cart...")
        add_json_response = session.post('http://127.0.0.1:5000/add_to_cart/Apple', 
                                        data={'quantity': 1})
        
        if add_json_response.status_code == 200:
            print("✅ JSON item add to cart successful")
        else:
            print(f"❌ JSON item add failed: {add_json_response.status_code}")
        
        # Step 5: Check cart again
        print("\n5️⃣ Checking cart page again...")
        final_cart_response = session.get('http://127.0.0.1:5000/cart')
        
        if final_cart_response.status_code == 200:
            final_cart_text = final_cart_response.text
            
            items_found = []
            if "Organic Bananas" in final_cart_text:
                items_found.append("Organic Bananas")
            if "Apple" in final_cart_text:
                items_found.append("Apple")
                
            if items_found:
                print(f"✅ Items found in cart: {', '.join(items_found)}")
            else:
                print("❌ No items found in cart")
                
            print(f"\n📄 Cart page contains:")
            print(f"  - 'Cart Items': {'✅' if 'Cart Items' in final_cart_text else '❌'}")
            print(f"  - 'Your cart is empty': {'❌' if 'Your cart is empty' in final_cart_text else '✅'}")
            print(f"  - '$0.00': {'❌' if '$0.00' in final_cart_text else '✅'}")
        
        print(f"\n✅ Cart session test completed!")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return

if __name__ == "__main__":
    test_cart_session()
