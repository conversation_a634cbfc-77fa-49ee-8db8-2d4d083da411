from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import uuid

db = SQLAlchemy()

class User(UserMixin, db.Model):
    """User model for authentication and user management"""
    
    __tablename__ = 'users'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    phone = db.Column(db.String(20), nullable=True)
    address = db.Column(db.Text, nullable=True)
    city = db.Column(db.String(50), nullable=True)
    state = db.Column(db.String(50), nullable=True)
    zip_code = db.Column(db.String(10), nullable=True)
    
    # User roles and status
    role = db.Column(db.String(20), nullable=False, default='customer')  # customer, admin, staff
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    is_verified = db.Column(db.Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    last_login = db.Column(db.DateTime, nullable=True)
    
    # Relationships
    orders = db.relationship('Order', backref='user', lazy=True, cascade='all, delete-orphan')
    cart_items = db.relationship('CartItem', backref='user', lazy=True, cascade='all, delete-orphan')
    
    def __init__(self, username, email, password, first_name, last_name, **kwargs):
        self.username = username
        self.email = email
        self.set_password(password)
        self.first_name = first_name
        self.last_name = last_name
        
        # Set optional fields
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def set_password(self, password):
        """Hash and set the user's password"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Check if the provided password matches the user's password"""
        return check_password_hash(self.password_hash, password)
    
    def get_full_name(self):
        """Return the user's full name"""
        return f"{self.first_name} {self.last_name}"
    
    def is_admin(self):
        """Check if user has admin role"""
        return self.role == 'admin'
    
    def is_staff(self):
        """Check if user has staff role"""
        return self.role in ['admin', 'staff']
    
    def update_last_login(self):
        """Update the last login timestamp"""
        self.last_login = datetime.utcnow()
        db.session.commit()
    
    def to_dict(self):
        """Convert user object to dictionary (excluding sensitive data)"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'full_name': self.get_full_name(),
            'phone': self.phone,
            'role': self.role,
            'is_active': self.is_active,
            'is_verified': self.is_verified,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }
    
    def __repr__(self):
        return f'<User {self.username}>'


class Order(db.Model):
    """Order model for tracking user purchases"""
    
    __tablename__ = 'orders'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False)
    order_number = db.Column(db.String(20), unique=True, nullable=False)
    status = db.Column(db.String(20), nullable=False, default='pending')  # pending, processing, shipped, delivered, cancelled
    total_amount = db.Column(db.Numeric(10, 2), nullable=False)
    
    # Shipping information
    shipping_address = db.Column(db.Text, nullable=False)
    shipping_city = db.Column(db.String(50), nullable=False)
    shipping_state = db.Column(db.String(50), nullable=False)
    shipping_zip = db.Column(db.String(10), nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    shipped_at = db.Column(db.DateTime, nullable=True)
    delivered_at = db.Column(db.DateTime, nullable=True)
    
    # Relationships
    order_items = db.relationship('OrderItem', backref='order', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Order {self.order_number}>'


class OrderItem(db.Model):
    """Order item model for individual items in an order"""
    
    __tablename__ = 'order_items'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    order_id = db.Column(db.String(36), db.ForeignKey('orders.id'), nullable=False)
    product_name = db.Column(db.String(200), nullable=False)
    product_price = db.Column(db.Numeric(10, 2), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    total_price = db.Column(db.Numeric(10, 2), nullable=False)
    
    def __repr__(self):
        return f'<OrderItem {self.product_name} x{self.quantity}>'


class CartItem(db.Model):
    """Cart item model for shopping cart functionality"""
    
    __tablename__ = 'cart_items'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False)
    product_name = db.Column(db.String(200), nullable=False)
    product_price = db.Column(db.Numeric(10, 2), nullable=False)
    quantity = db.Column(db.Integer, nullable=False, default=1)
    added_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    def get_total_price(self):
        """Calculate total price for this cart item"""
        return float(self.product_price) * self.quantity
    
    def __repr__(self):
        return f'<CartItem {self.product_name} x{self.quantity}>'


class Category(db.Model):
    """Category model for organizing products"""

    __tablename__ = 'categories'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(100), unique=True, nullable=False, index=True)
    slug = db.Column(db.String(120), unique=True, nullable=False, index=True)
    description = db.Column(db.Text, nullable=True)
    image = db.Column(db.String(255), nullable=True)
    icon = db.Column(db.String(50), nullable=True)  # Font Awesome icon class
    color = db.Column(db.String(7), nullable=True)  # Hex color code

    # Category hierarchy
    parent_id = db.Column(db.String(36), db.ForeignKey('categories.id'), nullable=True)
    parent = db.relationship('Category', remote_side=[id], backref='subcategories')

    # Status and ordering
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    sort_order = db.Column(db.Integer, default=0, nullable=False)

    # SEO fields
    meta_title = db.Column(db.String(200), nullable=True)
    meta_description = db.Column(db.Text, nullable=True)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    products = db.relationship('Product', backref='category', lazy=True)

    def __init__(self, name, **kwargs):
        self.name = name
        self.slug = self.generate_slug(name)

        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def generate_slug(self, name):
        """Generate URL-friendly slug from name"""
        import re
        slug = re.sub(r'[^\w\s-]', '', name.lower())
        slug = re.sub(r'[-\s]+', '-', slug)
        return slug.strip('-')

    def get_product_count(self):
        """Get count of active products in this category"""
        return Product.query.filter_by(category_id=self.id, is_active=True).count()

    def get_full_path(self):
        """Get full category path (for breadcrumbs)"""
        path = [self.name]
        parent = self.parent
        while parent:
            path.insert(0, parent.name)
            parent = parent.parent
        return ' > '.join(path)

    def to_dict(self):
        """Convert category to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'slug': self.slug,
            'description': self.description,
            'image': self.image,
            'icon': self.icon,
            'color': self.color,
            'is_active': self.is_active,
            'product_count': self.get_product_count(),
            'full_path': self.get_full_path(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    def __repr__(self):
        return f'<Category {self.name}>'


class Product(db.Model):
    """Product model for grocery items"""

    __tablename__ = 'products'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(200), nullable=False, index=True)
    slug = db.Column(db.String(220), unique=True, nullable=False, index=True)
    sku = db.Column(db.String(50), unique=True, nullable=True, index=True)
    barcode = db.Column(db.String(50), unique=True, nullable=True, index=True)

    # Basic information
    description = db.Column(db.Text, nullable=True)
    short_description = db.Column(db.String(500), nullable=True)
    ingredients = db.Column(db.Text, nullable=True)
    nutritional_info = db.Column(db.Text, nullable=True)

    # Category relationship
    category_id = db.Column(db.String(36), db.ForeignKey('categories.id'), nullable=False)

    # Pricing
    price = db.Column(db.Numeric(10, 2), nullable=False)
    cost_price = db.Column(db.Numeric(10, 2), nullable=True)  # For profit calculation
    compare_price = db.Column(db.Numeric(10, 2), nullable=True)  # Original price for discounts

    # Inventory
    stock_quantity = db.Column(db.Integer, default=0, nullable=False)
    low_stock_threshold = db.Column(db.Integer, default=10, nullable=False)
    track_inventory = db.Column(db.Boolean, default=True, nullable=False)
    allow_backorder = db.Column(db.Boolean, default=False, nullable=False)

    # Product specifications
    weight = db.Column(db.Numeric(8, 3), nullable=True)  # in kg
    dimensions = db.Column(db.String(100), nullable=True)  # LxWxH in cm
    unit = db.Column(db.String(20), nullable=True)  # kg, piece, liter, etc.
    unit_quantity = db.Column(db.Numeric(8, 3), default=1, nullable=False)

    # Status and visibility
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    is_featured = db.Column(db.Boolean, default=False, nullable=False)
    is_digital = db.Column(db.Boolean, default=False, nullable=False)
    requires_shipping = db.Column(db.Boolean, default=True, nullable=False)

    # SEO and marketing
    meta_title = db.Column(db.String(200), nullable=True)
    meta_description = db.Column(db.Text, nullable=True)
    tags = db.Column(db.String(500), nullable=True)  # Comma-separated tags

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    images = db.relationship('ProductImage', backref='product', lazy=True, cascade='all, delete-orphan')
    inventory_logs = db.relationship('InventoryLog', backref='product', lazy=True, cascade='all, delete-orphan')

    def __init__(self, name, category_id, price, **kwargs):
        self.name = name
        self.category_id = category_id
        self.price = price
        self.slug = self.generate_slug(name)

        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def generate_slug(self, name):
        """Generate URL-friendly slug from name"""
        import re
        slug = re.sub(r'[^\w\s-]', '', name.lower())
        slug = re.sub(r'[-\s]+', '-', slug)
        return slug.strip('-')

    def is_in_stock(self):
        """Check if product is in stock"""
        if not self.track_inventory:
            return True
        return self.stock_quantity > 0 or self.allow_backorder

    def is_low_stock(self):
        """Check if product is low in stock"""
        if not self.track_inventory:
            return False
        return self.stock_quantity <= self.low_stock_threshold

    def get_main_image(self):
        """Get the main product image"""
        main_image = ProductImage.query.filter_by(product_id=self.id, is_main=True).first()
        if main_image:
            return main_image.image_url

        # Fallback to first image
        first_image = ProductImage.query.filter_by(product_id=self.id).first()
        if first_image:
            return first_image.image_url

        return '/static/img/default-product.jpg'

    def get_all_images(self):
        """Get all product images"""
        return ProductImage.query.filter_by(product_id=self.id).order_by(ProductImage.sort_order).all()

    def get_discount_percentage(self):
        """Calculate discount percentage if compare_price is set"""
        if self.compare_price and self.compare_price > self.price:
            return round(((self.compare_price - self.price) / self.compare_price) * 100)
        return 0

    def get_profit_margin(self):
        """Calculate profit margin if cost_price is set"""
        if self.cost_price and self.cost_price > 0:
            return round(((self.price - self.cost_price) / self.price) * 100, 2)
        return 0

    def update_stock(self, quantity_change, reason="Manual adjustment", user_id=None):
        """Update stock quantity and log the change"""
        old_quantity = self.stock_quantity
        self.stock_quantity += quantity_change

        # Ensure stock doesn't go negative
        if self.stock_quantity < 0:
            self.stock_quantity = 0

        # Log the inventory change
        log = InventoryLog(
            product_id=self.id,
            old_quantity=old_quantity,
            new_quantity=self.stock_quantity,
            quantity_change=quantity_change,
            reason=reason,
            user_id=user_id
        )
        db.session.add(log)
        db.session.commit()

    def to_dict(self):
        """Convert product to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'slug': self.slug,
            'sku': self.sku,
            'description': self.description,
            'short_description': self.short_description,
            'category': self.category.name if self.category else None,
            'price': float(self.price),
            'compare_price': float(self.compare_price) if self.compare_price else None,
            'stock_quantity': self.stock_quantity,
            'is_in_stock': self.is_in_stock(),
            'is_low_stock': self.is_low_stock(),
            'is_active': self.is_active,
            'is_featured': self.is_featured,
            'main_image': self.get_main_image(),
            'discount_percentage': self.get_discount_percentage(),
            'unit': self.unit,
            'weight': float(self.weight) if self.weight else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    def __repr__(self):
        return f'<Product {self.name}>'





class ProductImage(db.Model):
    """Product image model for managing product photos"""

    __tablename__ = 'product_images'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    product_id = db.Column(db.String(36), db.ForeignKey('products.id'), nullable=False)

    # Image information
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255), nullable=False)
    image_url = db.Column(db.String(500), nullable=False)
    alt_text = db.Column(db.String(255), nullable=True)

    # Image properties
    file_size = db.Column(db.Integer, nullable=True)  # in bytes
    width = db.Column(db.Integer, nullable=True)
    height = db.Column(db.Integer, nullable=True)
    format = db.Column(db.String(10), nullable=True)  # jpg, png, webp, etc.

    # Organization
    is_main = db.Column(db.Boolean, default=False, nullable=False)
    sort_order = db.Column(db.Integer, default=0, nullable=False)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    def __init__(self, product_id, filename, original_filename, image_url, **kwargs):
        self.product_id = product_id
        self.filename = filename
        self.original_filename = original_filename
        self.image_url = image_url

        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def to_dict(self):
        """Convert image to dictionary"""
        return {
            'id': self.id,
            'product_id': self.product_id,
            'filename': self.filename,
            'original_filename': self.original_filename,
            'image_url': self.image_url,
            'alt_text': self.alt_text,
            'is_main': self.is_main,
            'sort_order': self.sort_order,
            'file_size': self.file_size,
            'width': self.width,
            'height': self.height,
            'format': self.format,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    def __repr__(self):
        return f'<ProductImage {self.filename}>'


class InventoryLog(db.Model):
    """Inventory log model for tracking stock changes"""

    __tablename__ = 'inventory_logs'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    product_id = db.Column(db.String(36), db.ForeignKey('products.id'), nullable=False)
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=True)

    # Stock change information
    old_quantity = db.Column(db.Integer, nullable=False)
    new_quantity = db.Column(db.Integer, nullable=False)
    quantity_change = db.Column(db.Integer, nullable=False)
    reason = db.Column(db.String(255), nullable=False)
    notes = db.Column(db.Text, nullable=True)

    # Reference information
    reference_type = db.Column(db.String(50), nullable=True)  # order, return, adjustment, etc.
    reference_id = db.Column(db.String(36), nullable=True)  # ID of related order, return, etc.

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    def __init__(self, product_id, old_quantity, new_quantity, quantity_change, reason, **kwargs):
        self.product_id = product_id
        self.old_quantity = old_quantity
        self.new_quantity = new_quantity
        self.quantity_change = quantity_change
        self.reason = reason

        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def to_dict(self):
        """Convert log to dictionary"""
        return {
            'id': self.id,
            'product_id': self.product_id,
            'user_id': self.user_id,
            'old_quantity': self.old_quantity,
            'new_quantity': self.new_quantity,
            'quantity_change': self.quantity_change,
            'reason': self.reason,
            'notes': self.notes,
            'reference_type': self.reference_type,
            'reference_id': self.reference_id,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    def __repr__(self):
        return f'<InventoryLog {self.product_id}: {self.quantity_change}>'


class PriceTier(db.Model):
    """Price tier model for bulk pricing"""

    __tablename__ = 'price_tiers'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    product_id = db.Column(db.String(36), db.ForeignKey('products.id'), nullable=False)

    # Tier information
    min_quantity = db.Column(db.Integer, nullable=False)
    max_quantity = db.Column(db.Integer, nullable=True)  # NULL means no upper limit
    price = db.Column(db.Numeric(10, 2), nullable=False)
    discount_percentage = db.Column(db.Numeric(5, 2), nullable=True)

    # Status
    is_active = db.Column(db.Boolean, default=True, nullable=False)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    def __init__(self, product_id, min_quantity, price, **kwargs):
        self.product_id = product_id
        self.min_quantity = min_quantity
        self.price = price

        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def to_dict(self):
        """Convert price tier to dictionary"""
        return {
            'id': self.id,
            'product_id': self.product_id,
            'min_quantity': self.min_quantity,
            'max_quantity': self.max_quantity,
            'price': float(self.price),
            'discount_percentage': float(self.discount_percentage) if self.discount_percentage else None,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    def __repr__(self):
        return f'<PriceTier {self.product_id}: {self.min_quantity}+ @ ${self.price}>'


class Promotion(db.Model):
    """Promotion model for promotional pricing"""

    __tablename__ = 'promotions'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text, nullable=True)
    code = db.Column(db.String(50), unique=True, nullable=True)  # Promo code

    # Promotion type
    promotion_type = db.Column(db.String(20), nullable=False)  # percentage, fixed_amount, buy_x_get_y

    # Discount values
    discount_percentage = db.Column(db.Numeric(5, 2), nullable=True)
    discount_amount = db.Column(db.Numeric(10, 2), nullable=True)

    # Buy X Get Y promotion
    buy_quantity = db.Column(db.Integer, nullable=True)
    get_quantity = db.Column(db.Integer, nullable=True)

    # Minimum requirements
    min_order_amount = db.Column(db.Numeric(10, 2), nullable=True)
    min_quantity = db.Column(db.Integer, nullable=True)

    # Date range
    start_date = db.Column(db.DateTime, nullable=True)
    end_date = db.Column(db.DateTime, nullable=True)

    # Usage limits
    usage_limit = db.Column(db.Integer, nullable=True)  # Total usage limit
    usage_limit_per_customer = db.Column(db.Integer, nullable=True)
    current_usage = db.Column(db.Integer, default=0, nullable=False)

    # Status
    is_active = db.Column(db.Boolean, default=True, nullable=False)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    def __init__(self, name, promotion_type, **kwargs):
        self.name = name
        self.promotion_type = promotion_type

        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def is_valid(self):
        """Check if promotion is currently valid"""
        if not self.is_active:
            return False

        now = datetime.utcnow()

        if self.start_date and now < self.start_date:
            return False

        if self.end_date and now > self.end_date:
            return False

        if self.usage_limit and self.current_usage >= self.usage_limit:
            return False

        return True

    def calculate_discount(self, amount, quantity=1):
        """Calculate discount for given amount and quantity"""
        if not self.is_valid():
            return 0

        if self.min_order_amount and amount < self.min_order_amount:
            return 0

        if self.min_quantity and quantity < self.min_quantity:
            return 0

        if self.promotion_type == 'percentage':
            return amount * (self.discount_percentage / 100)
        elif self.promotion_type == 'fixed_amount':
            return min(self.discount_amount, amount)
        elif self.promotion_type == 'buy_x_get_y':
            if quantity >= self.buy_quantity:
                free_items = (quantity // self.buy_quantity) * self.get_quantity
                return free_items * (amount / quantity)  # Price per item * free items

        return 0

    def to_dict(self):
        """Convert promotion to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'code': self.code,
            'promotion_type': self.promotion_type,
            'discount_percentage': float(self.discount_percentage) if self.discount_percentage else None,
            'discount_amount': float(self.discount_amount) if self.discount_amount else None,
            'buy_quantity': self.buy_quantity,
            'get_quantity': self.get_quantity,
            'min_order_amount': float(self.min_order_amount) if self.min_order_amount else None,
            'min_quantity': self.min_quantity,
            'start_date': self.start_date.isoformat() if self.start_date else None,
            'end_date': self.end_date.isoformat() if self.end_date else None,
            'usage_limit': self.usage_limit,
            'usage_limit_per_customer': self.usage_limit_per_customer,
            'current_usage': self.current_usage,
            'is_active': self.is_active,
            'is_valid': self.is_valid(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    def __repr__(self):
        return f'<Promotion {self.name}>'


def init_db(app):
    """Initialize the database with the Flask app"""
    db.init_app(app)

    with app.app_context():
        # Create all tables
        db.create_all()

        # Create default admin user if it doesn't exist
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                password='admin123',
                first_name='Admin',
                last_name='User',
                role='admin',
                is_verified=True
            )
            db.session.add(admin_user)
            db.session.commit()
            print("Default admin user created: admin/admin123")

        # Create default categories if they don't exist
        if Category.query.count() == 0:
            categories = [
                Category(name='Fruits & Vegetables', description='Fresh fruits and vegetables', icon='fas fa-apple-alt', color='#4CAF50'),
                Category(name='Dairy & Eggs', description='Fresh dairy products and eggs', icon='fas fa-cheese', color='#FFC107'),
                Category(name='Meat & Seafood', description='Fresh meat and seafood', icon='fas fa-fish', color='#FF5722'),
                Category(name='Bakery', description='Fresh bread and baked goods', icon='fas fa-bread-slice', color='#795548'),
                Category(name='Pantry', description='Pantry staples and dry goods', icon='fas fa-box', color='#607D8B'),
                Category(name='Beverages', description='Drinks and beverages', icon='fas fa-wine-bottle', color='#2196F3'),
                Category(name='Snacks', description='Snacks and confectionery', icon='fas fa-cookie-bite', color='#E91E63'),
                Category(name='Frozen Foods', description='Frozen meals and ingredients', icon='fas fa-snowflake', color='#00BCD4'),
            ]

            for category in categories:
                db.session.add(category)

            db.session.commit()
            print("Default categories created")

        # Create sample products if they don't exist
        if Product.query.count() == 0:
            fruits_category = Category.query.filter_by(name='Fruits & Vegetables').first()
            dairy_category = Category.query.filter_by(name='Dairy & Eggs').first()
            bakery_category = Category.query.filter_by(name='Bakery').first()

            if fruits_category and dairy_category and bakery_category:
                products = [
                    Product(name='Fresh Red Apples', category_id=fruits_category.id, price=3.99,
                           description='Crisp and sweet red apples, perfect for snacking',
                           stock_quantity=50, unit='kg', weight=1.0, sku='APPLE-RED-001'),
                    Product(name='Organic Bananas', category_id=fruits_category.id, price=2.49,
                           description='Organic bananas, naturally ripened',
                           stock_quantity=30, unit='bunch', sku='BANANA-ORG-001'),
                    Product(name='Fresh Carrots', category_id=fruits_category.id, price=1.99,
                           description='Fresh orange carrots, great for cooking',
                           stock_quantity=40, unit='kg', weight=1.0, sku='CARROT-001'),
                    Product(name='Whole Milk', category_id=dairy_category.id, price=4.50,
                           description='Fresh whole milk, 1 liter',
                           stock_quantity=25, unit='liter', sku='MILK-WHOLE-001'),
                    Product(name='Free Range Eggs', category_id=dairy_category.id, price=5.99,
                           description='Free range eggs, dozen pack',
                           stock_quantity=20, unit='dozen', sku='EGGS-FR-001'),
                    Product(name='Artisan Bread', category_id=bakery_category.id, price=4.25,
                           description='Freshly baked artisan bread',
                           stock_quantity=15, unit='loaf', sku='BREAD-ART-001'),
                ]

                for product in products:
                    db.session.add(product)

                db.session.commit()
                print("Sample products created")


class Payment(db.Model):
    """Payment model to track all payment transactions"""

    __tablename__ = 'payments'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    payment_id = db.Column(db.String(100), unique=True, nullable=False)  # Unique payment identifier
    order_id = db.Column(db.String(100), nullable=False)  # Links to orders.json
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=True)  # Null for guest orders

    # Payment Details
    amount = db.Column(db.Numeric(10, 2), nullable=False)
    currency = db.Column(db.String(10), default='INR')
    payment_method = db.Column(db.String(50), nullable=False)  # 'upi', 'cod', 'card', 'wallet'
    payment_provider = db.Column(db.String(50))  # 'gpay', 'paytm', 'phonepe', 'razorpay', etc.

    # Status Tracking
    status = db.Column(db.String(50), default='pending')  # 'pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'
    transaction_id = db.Column(db.String(200))  # External transaction ID from payment gateway
    gateway_response = db.Column(db.Text)  # Store gateway response for debugging

    # UPI Specific Fields
    upi_id = db.Column(db.String(100))  # Customer's UPI ID
    upi_ref_id = db.Column(db.String(100))  # UPI reference number

    # COD Specific Fields
    cod_collected = db.Column(db.Boolean, default=False)
    cod_collected_at = db.Column(db.DateTime)
    cod_collected_by = db.Column(db.String(100))  # Delivery person name

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    completed_at = db.Column(db.DateTime)

    # Customer Information (for guest orders)
    customer_name = db.Column(db.String(100))
    customer_email = db.Column(db.String(100))
    customer_phone = db.Column(db.String(20))

    # Relationships
    user = db.relationship('User', backref='payments')

    def __repr__(self):
        return f'<Payment {self.payment_id}: {self.amount} {self.currency} via {self.payment_method}>'

    def to_dict(self):
        """Convert payment to dictionary for JSON serialization"""
        return {
            'payment_id': self.payment_id,
            'order_id': self.order_id,
            'amount': float(self.amount),
            'currency': self.currency,
            'payment_method': self.payment_method,
            'payment_provider': self.payment_provider,
            'status': self.status,
            'transaction_id': self.transaction_id,
            'upi_id': self.upi_id,
            'upi_ref_id': self.upi_ref_id,
            'cod_collected': self.cod_collected,
            'cod_collected_at': self.cod_collected_at.isoformat() if self.cod_collected_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'customer_name': self.customer_name,
            'customer_email': self.customer_email,
            'customer_phone': self.customer_phone
        }


class PaymentMethod(db.Model):
    """Available payment methods configuration"""

    __tablename__ = 'payment_methods'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    method_type = db.Column(db.String(50), nullable=False)  # 'upi', 'cod', 'card', 'wallet'
    provider_name = db.Column(db.String(100), nullable=False)  # 'GPay', 'Paytm', 'PhonePe', 'COD'
    display_name = db.Column(db.String(100), nullable=False)
    icon_url = db.Column(db.String(200))
    icon_class = db.Column(db.String(100))  # CSS class for icon
    is_active = db.Column(db.Boolean, default=True)
    sort_order = db.Column(db.Integer, default=0)

    # Configuration
    min_amount = db.Column(db.Numeric(10, 2), default=0)
    max_amount = db.Column(db.Numeric(10, 2))
    processing_fee = db.Column(db.Numeric(10, 2), default=0)
    processing_fee_percentage = db.Column(db.Numeric(5, 2), default=0)

    # UPI specific
    upi_app_package = db.Column(db.String(100))  # Android package name for deep linking
    upi_scheme = db.Column(db.String(50))  # UPI scheme for deep linking

    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<PaymentMethod {self.provider_name}: {self.method_type}>'

    @staticmethod
    def create_default_payment_methods():
        """Create default payment methods"""
        default_methods = [
            {
                'method_type': 'upi',
                'provider_name': 'gpay',
                'display_name': 'Google Pay',
                'icon_class': 'fab fa-google-pay',
                'sort_order': 1,
                'upi_app_package': 'com.google.android.apps.nbu.paisa.user',
                'upi_scheme': 'gpay'
            },
            {
                'method_type': 'upi',
                'provider_name': 'paytm',
                'display_name': 'Paytm',
                'icon_class': 'fas fa-mobile-alt',
                'sort_order': 2,
                'upi_app_package': 'net.one97.paytm',
                'upi_scheme': 'paytm'
            },
            {
                'method_type': 'upi',
                'provider_name': 'phonepe',
                'display_name': 'PhonePe',
                'icon_class': 'fas fa-phone',
                'sort_order': 3,
                'upi_app_package': 'com.phonepe.app',
                'upi_scheme': 'phonepe'
            },
            {
                'method_type': 'upi',
                'provider_name': 'bhim',
                'display_name': 'BHIM UPI',
                'icon_class': 'fas fa-university',
                'sort_order': 4,
                'upi_app_package': 'in.org.npci.upiapp',
                'upi_scheme': 'bhim'
            },
            {
                'method_type': 'upi',
                'provider_name': 'other_upi',
                'display_name': 'Other UPI Apps',
                'icon_class': 'fas fa-qrcode',
                'sort_order': 5,
                'upi_scheme': 'upi'
            },
            {
                'method_type': 'cod',
                'provider_name': 'cod',
                'display_name': 'Cash on Delivery',
                'icon_class': 'fas fa-money-bill-wave',
                'sort_order': 10,
                'min_amount': 50,
                'max_amount': 5000
            }
        ]

        for method_data in default_methods:
            existing = PaymentMethod.query.filter_by(provider_name=method_data['provider_name']).first()
            if not existing:
                method = PaymentMethod(**method_data)
                db.session.add(method)

        db.session.commit()
