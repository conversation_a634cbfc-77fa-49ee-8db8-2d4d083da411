{% extends "base.html" %}

{% block title %}Debug Orders{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h2>🔍 Order Tracking Debug Information</h2>

            <div class="card mt-4">
                <div class="card-header">
                    <h5>Debug Information</h5>
                </div>
                <div class="card-body">
                    <p><strong>Current User Email:</strong> {{ debug_info.current_user_email }}</p>
                    <p><strong>Total Orders in System:</strong> {{ debug_info.total_orders }}</p>
                    <p><strong>Customer Orders Found:</strong> {{ debug_info.customer_orders }}</p>
                </div>
            </div>

            {% if debug_info.orders %}
                <div class="card mt-4">
                    <div class="card-header">
                        <h5>Customer Orders</h5>
                    </div>
                    <div class="card-body">
                        {% for order in debug_info.orders %}
                            <div class="border p-3 mb-3">
                                <h6>Order #{{ order.order_id }}</h6>
                                <p><strong>Date:</strong> {{ order.date }}</p>
                                <p><strong>Status:</strong> {{ order.status or 'No status' }}</p>
                                <p><strong>Total:</strong> ${{ order.total }}</p>
                                <p><strong>Items:</strong> {{ order.items|length }} items</p>
                                {% if order.items %}
                                    <ul>
                                        {% for item in order.items %}
                                            <li>{{ item.name }} - Qty: {{ item.quantity }} - ${{ item.price }}</li>
                                        {% endfor %}
                                    </ul>
                                {% endif %}
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% else %}
                <div class="alert alert-warning mt-4">
                    <h5>⚠️ No Orders Found</h5>
                    <p>No orders found for email: <strong>{{ debug_info.current_user_email }}</strong></p>
                    <p>This means either:</p>
                    <ul>
                        <li>The user hasn't placed any orders yet</li>
                        <li>There's an email mismatch between user account and orders</li>
                        <li>There's an issue with the order loading function</li>
                    </ul>
                </div>
            {% endif %}

            <div class="mt-4">
                <a href="{{ url_for('profile') }}" class="btn btn-primary">Back to Profile</a>
                <a href="{{ url_for('my_orders') }}" class="btn btn-secondary">My Orders</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
