{% extends "base.html" %}
{% block title %}Checkout{% endblock %}
{% block content %}
<div class="container py-4">
    <h2 class="mb-4"><i class="fas fa-shopping-cart"></i> Checkout</h2>
    
    {% if cart and cart|length > 0 %}
    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h3>Your Information</h3>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('checkout') }}">
                        {% if current_user.is_authenticated %}
                        <!-- User is logged in - pre-fill their information -->
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Logged in as:</strong> {{ current_user.first_name }} {{ current_user.last_name }} ({{ current_user.email }})
                        </div>
                        <div class="mb-3">
                            <label for="name" class="form-label">Full Name</label>
                            <input type="text" class="form-control" id="name" name="name"
                                   value="{{ current_user.first_name }} {{ current_user.last_name }}" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email"
                                   value="{{ current_user.email }}" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="phone" name="phone"
                                   value="{{ current_user.phone or '' }}"
                                   {{ 'readonly' if current_user.phone else '' }} required>
                        </div>
                        <div class="mb-3">
                            <label for="address" class="form-label">Delivery Address</label>
                            <textarea class="form-control" id="address" name="address" rows="3"
                                      {{ 'readonly' if current_user.address else '' }} required>{{ current_user.address or '' }}</textarea>
                        </div>
                        {% else %}
                        <!-- User is not logged in - show empty form -->
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Guest Checkout:</strong> Consider <a href="{{ url_for('login') }}">logging in</a> to track your order easily.
                        </div>
                        <div class="mb-3">
                            <label for="name" class="form-label">Full Name</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        <div class="mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="phone" name="phone" required>
                        </div>
                        <div class="mb-3">
                            <label for="address" class="form-label">Delivery Address</label>
                            <textarea class="form-control" id="address" name="address" rows="3" required></textarea>
                        </div>
                        {% endif %}
                        <div class="d-grid">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-check-circle"></i> Place Order
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3>Order Summary</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <tbody>
                                {% for item in cart %}
                                <tr>
                                    <td>{{ item.name }} x {{ item.quantity }}</td>
                                    <td class="text-end">${{ "%.2f"|format(item.price * item.quantity) }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th>Total</th>
                                    <th class="text-end">${{ "%.2f"|format(total) }}</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    <a href="{{ url_for('view_cart') }}" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-arrow-left"></i> Back to Cart
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div class="alert alert-warning">
        <p>Your cart is empty. Please add some items before checking out.</p>
        <a href="{{ url_for('shop') }}" class="btn btn-primary">
            <i class="fas fa-shopping-cart"></i> Go Shopping
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}

