#!/usr/bin/env python3

"""
Simple Flask app to test cart functionality
"""

from flask import Flask, session, request, jsonify, render_template_string
import json

app = Flask(__name__)
app.secret_key = 'test_key_for_cart_debugging'

# Simple cart template
CART_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>Cart Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .cart-item { border: 1px solid #ddd; padding: 10px; margin: 5px 0; }
        .empty { color: #666; font-style: italic; }
        .total { font-weight: bold; font-size: 1.2em; }
        button { padding: 5px 10px; margin: 5px; }
    </style>
</head>
<body>
    <h1>🛒 Cart Test</h1>
    
    <h2>Add Items</h2>
    <button onclick="addItem('Organic Bananas', 2.49)">Add Organic Bananas ($2.49)</button>
    <button onclick="addItem('Apple', 0.99)">Add Apple ($0.99)</button>
    <button onclick="clearCart()">Clear Cart</button>
    
    <h2>Cart Contents</h2>
    <div id="cart-contents">
        {% if cart and cart|length > 0 %}
            {% for item in cart %}
            <div class="cart-item">
                <strong>{{ item.name }}</strong> - 
                Quantity: {{ item.quantity }} - 
                Price: ${{ "%.2f"|format(item.price) }} - 
                Subtotal: ${{ "%.2f"|format(item.price * item.quantity) }}
            </div>
            {% endfor %}
            <div class="total">Total: ${{ "%.2f"|format(total) }}</div>
        {% else %}
            <div class="empty">Your cart is empty</div>
        {% endif %}
    </div>
    
    <h2>Debug Info</h2>
    <div>
        <p>Cart exists: {{ cart is not none }}</p>
        <p>Cart length: {{ cart|length if cart else 0 }}</p>
        <p>Cart condition (cart and cart|length > 0): {{ cart and cart|length > 0 }}</p>
        <p>Session ID: {{ session_id }}</p>
    </div>
    
    <script>
        function addItem(name, price) {
            fetch('/add_item', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({name: name, price: price, quantity: 1})
            }).then(() => location.reload());
        }
        
        function clearCart() {
            fetch('/clear_cart', {method: 'POST'})
            .then(() => location.reload());
        }
    </script>
</body>
</html>
"""

@app.route('/')
def cart_test():
    """Display cart test page"""
    cart = session.get('cart', [])
    total = sum(item.get('price', 0) * item.get('quantity', 0) for item in cart)
    
    return render_template_string(CART_TEMPLATE, 
                                cart=cart, 
                                total=total,
                                session_id=session.get('_id', 'No ID'))

@app.route('/add_item', methods=['POST'])
def add_item():
    """Add item to cart"""
    data = request.get_json()
    name = data.get('name')
    price = data.get('price')
    quantity = data.get('quantity', 1)
    
    cart = session.get('cart', [])
    
    # Check if item already exists
    existing_item = next((item for item in cart if item['name'] == name), None)
    if existing_item:
        existing_item['quantity'] += quantity
    else:
        cart.append({
            'name': name,
            'price': price,
            'quantity': quantity
        })
    
    session['cart'] = cart
    print(f"Added {name} to cart. Cart now has {len(cart)} items")
    
    return jsonify({'success': True, 'cart_size': len(cart)})

@app.route('/clear_cart', methods=['POST'])
def clear_cart():
    """Clear cart"""
    session['cart'] = []
    print("Cart cleared")
    return jsonify({'success': True})

@app.route('/debug')
def debug():
    """Debug session info"""
    return jsonify({
        'session_data': dict(session),
        'cart': session.get('cart', []),
        'cart_length': len(session.get('cart', [])),
        'session_id': session.get('_id', 'No ID')
    })

if __name__ == '__main__':
    print("🧪 Starting simple cart test server...")
    print("Visit http://127.0.0.1:5001 to test cart functionality")
    app.run(debug=True, host='127.0.0.1', port=5001)
