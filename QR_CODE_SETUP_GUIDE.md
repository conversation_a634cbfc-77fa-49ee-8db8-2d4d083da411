# 🎯 QR Code Setup Guide for UPI Payments

## 📍 Where to Add Your QR Code

Your QR code should be added in the **UPI Payment page** (`templates/upi_payment.html`) in the QR code section.

## 🔧 3 Options to Add Your QR Code

### **Option 1: Static QR Code Image (Recommended for Testing)**

1. **Create your UPI QR code** using any UPI app or QR generator
2. **Save the image** as `static/images/upi-qr-code.png`
3. **Enable static QR code** in `templates/upi_payment.html`:

```html
<!-- Change this line from style="display: none;" to style="display: block;" -->
<div class="qr-code-image" style="display: block;">
    <img src="{{ url_for('static', filename='images/upi-qr-code.png') }}" 
         alt="UPI QR Code" 
         style="width: 200px; height: 200px; border-radius: 8px;">
</div>

<!-- Hide the API QR code -->
<div class="qr-code-api" style="display: none;">
```

### **Option 2: Dynamic QR Code (Recommended for Production)**

1. **Enable dynamic QR code** in `templates/upi_payment.html`:

```html
<!-- Change this line from style="display: none;" to style="display: block;" -->
<div class="qr-code-dynamic" style="display: block;">
    <img src="{{ url_for('generate_qr_code', payment_id=payment_id, amount=amount) }}" 
         alt="Dynamic UPI QR Code" 
         style="width: 200px; height: 200px; border-radius: 8px;">
</div>

<!-- Hide the API QR code -->
<div class="qr-code-api" style="display: none;">
```

2. **Install QR code library** (optional for local generation):
```bash
pip install qrcode[pil]
```

### **Option 3: External QR API (Currently Active)**

This is **currently enabled** and uses an external service to generate QR codes automatically.

**Pros:**
- ✅ Works immediately
- ✅ No setup required
- ✅ Dynamic content

**Cons:**
- ❌ Requires internet
- ❌ Depends on external service

## 🎯 How to Get Your UPI QR Code

### **Method 1: From Your UPI App**
1. Open **Google Pay**, **Paytm**, or **PhonePe**
2. Go to **"Receive Money"** or **"QR Code"**
3. **Download/Screenshot** your QR code
4. Save as `static/images/upi-qr-code.png`

### **Method 2: From Your Bank**
1. Login to your **bank's UPI service**
2. Generate **merchant QR code**
3. Download the QR code image
4. Save as `static/images/upi-qr-code.png`

### **Method 3: Create Custom QR Code**
1. Use online QR generator (like qr-code-generator.com)
2. **Input your UPI ID**: `yourname@paytm` or `yourname@okaxis`
3. **Generate QR code**
4. Download and save as `static/images/upi-qr-code.png`

## 📝 Update Your UPI ID

**Current UPI ID in code:** `grocerystore@upi`

**To change it:**

1. **In `templates/upi_payment.html`** - Update UPI ID display:
```html
<div class="upi-id-display">
    <strong>UPI ID:</strong> YOUR_ACTUAL_UPI_ID@paytm
    <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyUpiId()">
        <i class="fas fa-copy"></i> Copy
    </button>
</div>
```

2. **In JavaScript section** - Update copy function:
```javascript
function copyUpiId() {
    navigator.clipboard.writeText('YOUR_ACTUAL_UPI_ID@paytm').then(function() {
        alert('UPI ID copied to clipboard!');
    });
}
```

3. **In UPI app opening function** - Update UPI string:
```javascript
const upiString = `upi://pay?pa=YOUR_ACTUAL_UPI_ID@paytm&pn=Grocery Store&am={{ amount }}&cu=INR&tn=Order {{ order_id }}`;
```

## 🚀 Quick Setup Steps

### **For Immediate Testing:**

1. **Keep current setup** (external QR API is working)
2. **Update UPI ID** to your actual UPI ID
3. **Test the payment flow**

### **For Production:**

1. **Get your QR code** from UPI app
2. **Save as** `static/images/upi-qr-code.png`
3. **Enable static QR code** in template
4. **Update UPI ID** throughout the code
5. **Test thoroughly**

## 📱 Testing Your QR Code

1. **Open the payment page**
2. **Scan QR code** with any UPI app
3. **Verify details:**
   - ✅ Correct UPI ID
   - ✅ Correct amount
   - ✅ Order reference
4. **Complete test payment**

## 🔧 File Locations

- **QR Code Image:** `static/images/upi-qr-code.png`
- **Template File:** `templates/upi_payment.html`
- **Backend Route:** `app.py` (generate_qr_code function)

## 💡 Pro Tips

1. **Use high-quality QR codes** (at least 200x200 pixels)
2. **Test with multiple UPI apps** (GPay, Paytm, PhonePe)
3. **Keep UPI ID consistent** across all files
4. **Monitor QR code scanning** success rates
5. **Have backup payment methods** ready

Your QR code is now ready to accept real payments! 🎉
