# 🎯 QR Code Setup Guide for UPI Payments

## 📍 Where to Add Your QR Code

Your QR code should be added in the **UPI Payment page** (`templates/upi_payment.html`) in the QR code section.

## 🔧 3 Options to Add Your QR Code

### **Option 1: Static QR Code Image (Recommended for Testing)**

1. **Create your UPI QR code** using any UPI app or QR generator
2. **Save the image** as `static/images/upi-qr-code.png`
3. **Enable static QR code** in `templates/upi_payment.html`:

```html
<!-- Change this line from style="display: none;" to style="display: block;" -->
<div class="qr-code-image" style="display: block;">
    <img src="{{ url_for('static', filename='images/upi-qr-code.png') }}" 
         alt="UPI QR Code" 
         style="width: 200px; height: 200px; border-radius: 8px;">
</div>

<!-- Hide the API QR code -->
<div class="qr-code-api" style="display: none;">
```

### **Option 2: Dynamic QR Code (Recommended for Production)**

1. **Enable dynamic QR code** in `templates/upi_payment.html`:

```html
<!-- Change this line from style="display: none;" to style="display: block;" -->
<div class="qr-code-dynamic" style="display: block;">
    <img src="{{ url_for('generate_qr_code', payment_id=payment_id, amount=amount) }}" 
         alt="Dynamic UPI QR Code" 
         style="width: 200px; height: 200px; border-radius: 8px;">
</div>

<!-- Hide the API QR code -->
<div class="qr-code-api" style="display: none;">
```

2. **Install QR code library** (optional for local generation):
```bash
pip install qrcode[pil]
```

### **Option 3: External QR API (Currently Active)**

This is **currently enabled** and uses an external service to generate QR codes automatically.

**Pros:**
- ✅ Works immediately
- ✅ No setup required
- ✅ Dynamic content

**Cons:**
- ❌ Requires internet
- ❌ Depends on external service

## 🎯 How to Get Your UPI QR Code

### **Method 1: From Your UPI App**
1. Open **Google Pay**, **Paytm**, or **PhonePe**
2. Go to **"Receive Money"** or **"QR Code"**
3. **Download/Screenshot** your QR code
4. Save as `static/images/upi-qr-code.png`

### **Method 2: From Your Bank**
1. Login to your **bank's UPI service**
2. Generate **merchant QR code**
3. Download the QR code image
4. Save as `static/images/upi-qr-code.png`

### **Method 3: Create Custom QR Code**
1. Use online QR generator (like qr-code-generator.com)
2. **Input your UPI ID**: `yourname@paytm` or `yourname@okaxis`
3. **Generate QR code**
4. Download and save as `static/images/upi-qr-code.png`

## ✅ UPI ID Updated!

**Current UPI ID in code:** `sksajarulhoque@okicici` ✅

**Your UPI ID has been updated throughout the system:**

1. ✅ **QR Code generation** - Updated to your UPI ID
2. ✅ **UPI ID display** - Shows your actual UPI ID
3. ✅ **Copy function** - Copies your UPI ID
4. ✅ **App deep links** - Uses your UPI ID
5. ✅ **Payment instructions** - Shows your UPI ID

**Your payment system is now configured with:**
- **UPI ID:** `sksajarulhoque@okicici`
- **Bank:** ICICI Bank
- **QR Code:** Auto-generated with your UPI ID

## 🚀 Quick Setup Steps

### **For Immediate Testing:**

1. **Keep current setup** (external QR API is working)
2. **Update UPI ID** to your actual UPI ID
3. **Test the payment flow**

### **For Production:**

1. **Get your QR code** from UPI app
2. **Save as** `static/images/upi-qr-code.png`
3. **Enable static QR code** in template
4. **Update UPI ID** throughout the code
5. **Test thoroughly**

## 📱 Testing Your QR Code

1. **Open the payment page**
2. **Scan QR code** with any UPI app
3. **Verify details:**
   - ✅ Correct UPI ID
   - ✅ Correct amount
   - ✅ Order reference
4. **Complete test payment**

## 🔧 File Locations

- **QR Code Image:** `static/images/upi-qr-code.png`
- **Template File:** `templates/upi_payment.html`
- **Backend Route:** `app.py` (generate_qr_code function)

## 💡 Pro Tips

1. **Use high-quality QR codes** (at least 200x200 pixels)
2. **Test with multiple UPI apps** (GPay, Paytm, PhonePe)
3. **Keep UPI ID consistent** across all files
4. **Monitor QR code scanning** success rates
5. **Have backup payment methods** ready

Your QR code is now ready to accept real payments! 🎉
