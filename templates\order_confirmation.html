{% extends "base.html" %}
{% block title %}Order Confirmation{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3 class="mb-0"><i class="fas fa-check-circle"></i> Order Confirmed!</h3>
                </div>
                <div class="card-body text-center">
                    <div class="confirmation-animation mb-4">
                        <div class="checkmark-circle">
                            <i class="fas fa-check-circle text-success fa-5x"></i>
                        </div>
                        <h2 class="mt-3 text-success">Thank You!</h2>
                        <p class="lead">Your order has been received and is being processed.</p>
                    </div>
                    
                    <div class="order-details mt-4">
                        <h4>Order Details</h4>
                        <p><strong>Order ID:</strong> {{ order.get('order_id', '') }}</p>
                        <p><strong>Date:</strong> {{ order.get('date', '') }}</p>
                        <p><strong>Name:</strong> {{ order.get('name', '') }}</p>
                        <p><strong>Email:</strong> {{ order.get('email', '') }}</p>
                        <p><strong>Phone:</strong> {{ order.get('phone', '') }}</p>
                        <p><strong>Address:</strong> {{ order.get('address', '') }}</p>
                        
                        <h4>Items Ordered</h4>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Item</th>
                                        <th>Price</th>
                                        <th>Quantity</th>
                                        <th>Subtotal</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for item in order.get('items', []) %}
                                    <tr>
                                        <td>{{ item.get('name', '') }}</td>
                                        <td>${{ "%.2f"|format(item.get('price', 0)) }}</td>
                                        <td>{{ item.get('quantity', 0) }}</td>
                                        <td>${{ "%.2f"|format(item.get('price', 0) * item.get('quantity', 0)) }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <th colspan="3" class="text-end">Total:</th>
                                        <th>${{ "%.2f"|format(order.get('total', 0)) }}</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                        
                        <!-- Order Tracking Information -->
                        <div class="alert alert-info mt-4">
                            <h5><i class="fas fa-info-circle"></i> Track Your Order</h5>
                            {% if current_user.is_authenticated %}
                                <p>You can track this order anytime by visiting your profile or orders page.</p>
                                <div class="d-flex gap-2 flex-wrap">
                                    <a href="{{ url_for('track_order', order_id=order.get('order_id')) }}" class="btn btn-success btn-sm">
                                        <i class="fas fa-search"></i> Track This Order
                                    </a>
                                    <a href="{{ url_for('my_orders') }}" class="btn btn-outline-success btn-sm">
                                        <i class="fas fa-list"></i> View All Orders
                                    </a>
                                    <a href="{{ url_for('profile') }}" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-user"></i> My Profile
                                    </a>
                                </div>
                            {% else %}
                                <p><strong>Account Created!</strong> You can now login to track your orders:</p>
                                <p><strong>Email:</strong> {{ order.get('email') }}<br>
                                   <strong>Password:</strong> defaultpassword123</p>
                                <div class="d-flex gap-2 flex-wrap">
                                    <a href="{{ url_for('login') }}" class="btn btn-success btn-sm">
                                        <i class="fas fa-sign-in-alt"></i> Login to Track Orders
                                    </a>
                                    <a href="{{ url_for('track_order', order_id=order.get('order_id')) }}" class="btn btn-outline-success btn-sm">
                                        <i class="fas fa-search"></i> Track This Order
                                    </a>
                                </div>
                                <small class="text-muted">
                                    <i class="fas fa-lightbulb"></i>
                                    Tip: Change your password after logging in for security.
                                </small>
                            {% endif %}
                        </div>

                        <div class="mt-4">
                            <a href="{{ url_for('shop') }}" class="btn btn-primary">
                                <i class="fas fa-shopping-cart"></i> Continue Shopping
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Simple animation for the confirmation page
    document.addEventListener('DOMContentLoaded', function() {
        // Add animation class to the checkmark
        const checkmark = document.querySelector('.checkmark-circle i');
        checkmark.classList.add('animate__animated', 'animate__bounceIn');
        
        // Add a simple fade-in effect to the order details
        const orderDetails = document.querySelector('.order-details');
        orderDetails.style.opacity = '0';
        orderDetails.style.transition = 'opacity 1s ease';
        
        // Delay the fade-in to allow the checkmark animation to complete
        setTimeout(function() {
            orderDetails.style.opacity = '1';
        }, 1000);
    });
</script>
{% endblock %}

{% block extra_css %}
<style>
    /* Add animate.css for simple animations */
    @import url('https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css');
    
    .confirmation-animation {
        padding: 2rem 0;
    }
    
    .checkmark-circle {
        margin-bottom: 1rem;
    }
    
    .checkmark-circle i {
        color: #28a745;
    }
    
    .order-details {
        transition: opacity 1s ease;
    }
    
    .card {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        border: none;
    }
</style>
{% endblock %}



