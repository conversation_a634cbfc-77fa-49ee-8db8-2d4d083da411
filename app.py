import os
import json
import uuid
import logging
from datetime import datetime
from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify
from flask_login import Login<PERSON>anager, login_user, logout_user, login_required, current_user
from werkzeug.exceptions import HTTPException
from werkzeug.utils import secure_filename
try:
    from werkzeug.urls import url_parse
except ImportError:
    from urllib.parse import urlparse as url_parse
from functools import wraps
from PIL import Image
import secrets
from models import db, User, Category, Product, ProductImage, InventoryLog
from forms import (LoginForm, RegistrationForm, ProfileForm, ChangePasswordForm, AdminUserForm,
                   CategoryForm, ProductForm, ProductImageForm, InventoryAdjustmentForm, ProductSearchForm)

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)

app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', 'dev_key_for_testing_only_change_in_production')
# Use default Flask session (in-memory) for simplicity and reliability
app.config['PERMANENT_SESSION_LIFETIME'] = 1800  # 30 minutes

# Database configuration
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///grocery_store.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# CSRF Protection
app.config['WTF_CSRF_ENABLED'] = True
app.config['WTF_CSRF_TIME_LIMIT'] = None

# Image upload configuration
app.config['UPLOAD_FOLDER'] = os.path.join(app.root_path, 'static', 'uploads')
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}

# Create upload directories if they don't exist
os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'products'), exist_ok=True)
os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'categories'), exist_ok=True)

# Initialize Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Please log in to access this page.'
login_manager.login_message_category = 'info'

# Initialize database (will be done in main function)
db.init_app(app)


# ===== IMAGE UPLOAD UTILITIES =====

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


def generate_unique_filename(filename):
    """Generate a unique filename while preserving extension"""
    ext = filename.rsplit('.', 1)[1].lower()
    unique_filename = f"{secrets.token_hex(16)}.{ext}"
    return unique_filename


def resize_image(image_path, max_width=800, max_height=600, quality=85):
    """Resize image while maintaining aspect ratio"""
    try:
        with Image.open(image_path) as img:
            # Convert RGBA to RGB if necessary
            if img.mode in ('RGBA', 'LA', 'P'):
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                img = background

            # Calculate new dimensions
            width, height = img.size
            if width > max_width or height > max_height:
                ratio = min(max_width / width, max_height / height)
                new_width = int(width * ratio)
                new_height = int(height * ratio)
                img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

            # Save optimized image
            img.save(image_path, 'JPEG', quality=quality, optimize=True)
            return img.size
    except Exception as e:
        app.logger.error(f"Error resizing image {image_path}: {str(e)}")
        return None


def save_product_image(image_file, product_id):
    """Save and process product image"""
    if not image_file or not allowed_file(image_file.filename):
        return None

    try:
        # Generate unique filename
        filename = generate_unique_filename(image_file.filename)

        # Create product-specific directory
        product_dir = os.path.join(app.config['UPLOAD_FOLDER'], 'products', product_id)
        os.makedirs(product_dir, exist_ok=True)

        # Save original file
        file_path = os.path.join(product_dir, filename)
        image_file.save(file_path)

        # Get file info before resizing
        file_size = os.path.getsize(file_path)

        # Resize image
        dimensions = resize_image(file_path)
        if not dimensions:
            os.remove(file_path)
            return None

        # Create ProductImage record
        image_url = f"/static/uploads/products/{product_id}/{filename}"
        product_image = ProductImage(
            product_id=product_id,
            filename=filename,
            original_filename=secure_filename(image_file.filename),
            image_url=image_url,
            file_size=file_size,
            width=dimensions[0],
            height=dimensions[1],
            format='JPEG'
        )

        db.session.add(product_image)
        db.session.commit()

        return product_image

    except Exception as e:
        app.logger.error(f"Error saving product image: {str(e)}")
        return None


@login_manager.user_loader
def load_user(user_id):
    """Load user by ID for Flask-Login"""
    return User.query.get(user_id)


# Security headers
@app.after_request
def add_security_headers(response):
    """Add security headers to all responses"""
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
    return response


# Rate limiting helper
from collections import defaultdict
from datetime import datetime, timedelta

# Simple in-memory rate limiting (use Redis in production)
rate_limit_storage = defaultdict(list)

def rate_limit(max_requests=5, window_minutes=15):
    """Simple rate limiting decorator"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
            now = datetime.utcnow()
            window_start = now - timedelta(minutes=window_minutes)

            # Clean old requests
            rate_limit_storage[client_ip] = [
                req_time for req_time in rate_limit_storage[client_ip]
                if req_time > window_start
            ]

            # Check if limit exceeded
            if len(rate_limit_storage[client_ip]) >= max_requests:
                flash('Too many requests. Please try again later.', 'error')
                return redirect(url_for('home'))

            # Add current request
            rate_limit_storage[client_ip].append(now)

            return f(*args, **kwargs)
        return decorated_function
    return decorator

# Helper functions
def load_grocery_data():
    try:
        if os.path.exists('grocery_data.json'):
            with open('grocery_data.json', 'r') as f:
                data = json.load(f)
                if not isinstance(data, list):
                    app.logger.error(f"grocery_data.json did not contain a list: {type(data)}")
                    return []
                return data
        return []
    except json.JSONDecodeError:
        app.logger.error("Error decoding grocery_data.json")
        return []
    except Exception as e:
        app.logger.error(f"Unexpected error loading grocery data: {str(e)}")
        return []

def save_grocery_data(items):
    try:
        if not isinstance(items, list):
            app.logger.error(f"Cannot save grocery data: not a list: {type(items)}")
            items = []
            
        with open('grocery_data.json', 'w') as f:
            json.dump(items, f, indent=4)
    except Exception as e:
        app.logger.error(f"Error saving grocery data: {str(e)}")

def load_orders():
    try:
        if os.path.exists('orders.json'):
            with open('orders.json', 'r') as f:
                data = json.load(f)
                if not isinstance(data, list):
                    app.logger.error(f"orders.json did not contain a list: {type(data)}")
                    return []
                return data
        return []
    except json.JSONDecodeError:
        app.logger.error("Error decoding orders.json")
        return []
    except Exception as e:
        app.logger.error(f"Unexpected error loading orders: {str(e)}")
        return []

def save_orders(orders):
    try:
        if not isinstance(orders, list):
            app.logger.error(f"Cannot save orders: not a list: {type(orders)}")
            orders = []
            
        with open('orders.json', 'w') as f:
            json.dump(orders, f, indent=4)
    except Exception as e:
        app.logger.error(f"Error saving orders: {str(e)}")

def load_users():
    try:
        if os.path.exists('users.json'):
            with open('users.json', 'r') as f:
                data = json.load(f)
                if not isinstance(data, list):
                    app.logger.error(f"users.json did not contain a list: {type(data)}")
                    return []
                return data
        return []
    except json.JSONDecodeError:
        app.logger.error("Error decoding users.json")
        return []
    except Exception as e:
        app.logger.error(f"Unexpected error loading users: {str(e)}")
        return []

def save_users(users):
    try:
        if not isinstance(users, list):
            app.logger.error(f"Cannot save users: not a list: {type(users)}")
            users = []
            
        with open('users.json', 'w') as f:
            json.dump(users, f, indent=4)
    except Exception as e:
        app.logger.error(f"Error saving users: {str(e)}")

# Admin authentication decorator
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('admin_logged_in'):
            flash("Please login first!")
            return redirect(url_for('admin_login'))
        return f(*args, **kwargs)
    return decorated_function

# Routes
@app.route('/')
@app.route('/home')
def home():
    """Home page with featured items from database"""
    try:
        # Get featured products from database
        featured_products = Product.query.filter_by(is_featured=True, is_active=True).limit(4).all()

        # If no featured products, get recent products
        if not featured_products:
            featured_products = Product.query.filter_by(is_active=True).order_by(Product.created_at.desc()).limit(4).all()

        app.logger.debug(f"Selected {len(featured_products)} featured products")

        # Get categories for navigation
        categories = Category.query.filter_by(is_active=True, parent_id=None).order_by(Category.sort_order, Category.name).limit(6).all()

        return render_template('home.html', featured_items=featured_products, categories=categories)
    except Exception as e:
        import traceback
        app.logger.error(f"Error in home route: {str(e)}")
        app.logger.error(f"Traceback: {traceback.format_exc()}")
        flash(f"Error loading homepage: {str(e)}")
        return render_template('home.html', featured_items=[], categories=[])

@app.route('/shop')
def shop():
    """Shop page with all products from database"""
    try:
        # Get all active products
        query = Product.query.filter_by(is_active=True)

        # Filter by category if specified
        category_id = request.args.get('category_id')
        if category_id:
            query = query.filter_by(category_id=category_id)

        # Filter by price if specified
        min_price = request.args.get('min_price')
        max_price = request.args.get('max_price')

        if min_price:
            try:
                min_price = float(min_price)
                query = query.filter(Product.price >= min_price)
            except ValueError:
                pass

        if max_price:
            try:
                max_price = float(max_price)
                query = query.filter(Product.price <= max_price)
            except ValueError:
                pass

        # Apply sorting
        sort_by = request.args.get('sort_by', 'name')
        if sort_by == 'price_asc':
            query = query.order_by(Product.price.asc())
        elif sort_by == 'price_desc':
            query = query.order_by(Product.price.desc())
        elif sort_by == 'name_desc':
            query = query.order_by(Product.name.desc())
        else:  # name_asc
            query = query.order_by(Product.name.asc())

        products = query.all()

        # Get categories for the sidebar
        categories = Category.query.filter_by(is_active=True, parent_id=None).order_by(Category.sort_order, Category.name).all()

        return render_template('shop.html', title='Shop', products=products, categories=categories)
    except Exception as e:
        app.logger.error(f"Error in shop route: {str(e)}")
        flash(f"Error loading shop: {str(e)}")
        return redirect(url_for('home'))

@app.route('/add_to_cart/<item_name>', methods=['POST'])
def add_to_cart(item_name):
    try:
        app.logger.info(f"🛒 Adding item to cart: {item_name}")

        # First try to find product in database
        product = Product.query.filter_by(name=item_name, is_active=True).first()

        if not product:
            app.logger.info(f"📄 Product '{item_name}' not found in database, trying JSON fallback")
            # Fallback to JSON data for backward compatibility
            items = load_grocery_data()
            item = next((i for i in items if i['name'] == item_name), None)

            if not item:
                app.logger.error(f"❌ Item '{item_name}' not found in database or JSON!")
                flash(f"Item '{item_name}' not found!")
                return redirect(url_for('shop'))

            # Use JSON data
            available_quantity = item['quantity']
            price = item['price']
            image = item.get('image', 'default.jpg')
            app.logger.info(f"📄 Using JSON data: price=${price}, stock={available_quantity}")
        else:
            # Use database product
            available_quantity = product.stock_quantity
            price = float(product.price)
            image = product.get_main_image()  # This returns the full path like '/static/img/default-product.jpg'
            app.logger.info(f"🗄️ Using database product: price=${price}, stock={available_quantity}, image={image}")

        quantity = int(request.form.get('quantity', 1))
        app.logger.info(f"📊 Requested quantity: {quantity}")

        if quantity <= 0:
            flash("Quantity must be positive!")
            return redirect(url_for('shop'))

        if quantity > available_quantity:
            flash(f"Sorry, only {available_quantity} units of {item_name} are available.")
            return redirect(url_for('shop'))

        cart = session.get('cart', [])
        app.logger.info(f"🛒 Current cart has {len(cart)} items")

        # Check if item already in cart
        cart_item = next((i for i in cart if i['name'] == item_name), None)
        if cart_item:
            cart_item['quantity'] += quantity
            app.logger.info(f"📈 Updated existing item quantity to {cart_item['quantity']}")
        else:
            new_item = {
                'name': item_name,
                'price': price,
                'quantity': quantity,
                'image': image
            }
            cart.append(new_item)
            app.logger.info(f"➕ Added new item to cart: {new_item}")

        session['cart'] = cart
        session.permanent = True  # Make session persistent
        app.logger.info(f"💾 Cart saved to session. Total items: {len(cart)}")
        flash(f"Added {quantity} {item_name} to cart!")
        return redirect(url_for('shop'))
    except ValueError:
        app.logger.error(f"❌ Invalid quantity for {item_name}")
        flash("Please enter a valid quantity!")
        return redirect(url_for('shop'))
    except Exception as e:
        app.logger.error(f"❌ Error in add_to_cart: {str(e)}")
        flash(f"Error adding to cart: {str(e)}")
        return redirect(url_for('shop'))

@app.route('/cart')
def view_cart():
    try:
        cart = session.get('cart', [])
        app.logger.info(f"🛒 Viewing cart with {len(cart)} items")

        for i, item in enumerate(cart):
            app.logger.info(f"  Item {i+1}: {item}")

        total = sum(item.get('price', 0) * item.get('quantity', 0) for item in cart)
        app.logger.info(f"💰 Cart total: ${total:.2f}")

        return render_template('cart.html', cart=cart, total=total)
    except Exception as e:
        app.logger.error(f"❌ Error in view_cart: {str(e)}")
        flash(f"Error viewing cart: {str(e)}")
        return redirect(url_for('shop'))

@app.route('/debug_cart')
def debug_cart():
    """Debug route to check cart session data"""
    try:
        cart = session.get('cart', [])
        session_data = dict(session)

        debug_info = {
            'cart_exists': 'cart' in session,
            'cart_length': len(cart),
            'cart_data': cart,
            'session_keys': list(session_data.keys()),
            'session_id': session.get('_id', 'No session ID'),
            'total_calculated': sum(item.get('price', 0) * item.get('quantity', 0) for item in cart)
        }

        return f"""
        <h1>🔍 Cart Debug Information</h1>
        <pre>{json.dumps(debug_info, indent=2)}</pre>
        <hr>
        <h2>Add Test Item</h2>
        <form action="/add_to_cart/Test Item" method="post">
            <input type="hidden" name="quantity" value="1">
            <button type="submit">Add Test Item to Cart</button>
        </form>
        <hr>
        <a href="/cart">View Cart</a> | <a href="/shop">Shop</a>
        """
    except Exception as e:
        return f"Error in debug: {str(e)}"

@app.route('/update_cart/<item_name>', methods=['POST'])
def update_cart(item_name):
    try:
        cart = session.get('cart', [])
        quantity = int(request.form.get('quantity', 0))

        if quantity <= 0:
            # Remove item from cart
            cart = [item for item in cart if item['name'] != item_name]
            flash(f"Removed {item_name} from cart!")
        else:
            # Update quantity
            cart_item = next((i for i in cart if i['name'] == item_name), None)
            if cart_item:
                # Check if requested quantity is available
                # First try database
                product = Product.query.filter_by(name=item_name, is_active=True).first()

                if product:
                    available_quantity = product.stock_quantity
                else:
                    # Fallback to JSON data
                    items = load_grocery_data()
                    stock_item = next((i for i in items if i['name'] == item_name), None)
                    available_quantity = stock_item['quantity'] if stock_item else 0

                if quantity > available_quantity:
                    flash(f"Sorry, only {available_quantity} units of {item_name} are available.")
                    quantity = available_quantity

                cart_item['quantity'] = quantity
                flash(f"Updated {item_name} quantity to {quantity}!")

        session['cart'] = cart
        return redirect(url_for('view_cart'))
    except ValueError:
        flash("Please enter a valid quantity!")
        return redirect(url_for('view_cart'))
    except Exception as e:
        app.logger.error(f"Error in update_cart: {str(e)}")
        flash(f"Error updating cart: {str(e)}")
        return redirect(url_for('view_cart'))

@app.route('/remove_from_cart/<item_name>')
def remove_from_cart(item_name):
    try:
        cart = session.get('cart', [])
        cart = [item for item in cart if item['name'] != item_name]
        session['cart'] = cart
        flash(f"Removed {item_name} from cart!")
        return redirect(url_for('view_cart'))
    except Exception as e:
        app.logger.error(f"Error in remove_from_cart: {str(e)}")
        flash(f"Error removing from cart: {str(e)}")
        return redirect(url_for('view_cart'))

@app.route('/checkout', methods=['GET', 'POST'])
def checkout():
    if 'cart' not in session or not session['cart']:
        flash("Your cart is empty!")
        return redirect(url_for('shop'))
    
    if request.method == 'POST':
        try:
            # Get form data
            name = request.form.get('name')
            email = request.form.get('email')
            phone = request.form.get('phone')
            address = request.form.get('address')

            # If user is logged in, use their email for the order
            if current_user.is_authenticated:
                email = current_user.email
                name = f"{current_user.first_name} {current_user.last_name}"
                phone = current_user.phone or phone
                address = current_user.address or address

            # Validate form data
            if not all([name, email, phone, address]):
                flash("Please fill out all fields!")
                return redirect(url_for('checkout'))
            
            # Create order
            cart = session.get('cart', [])
            items = []
            total = 0

            # Load grocery data for category information
            grocery_items = load_grocery_data()
            grocery_lookup = {item.get('name', '').lower(): item for item in grocery_items}

            # Process each item in cart
            for cart_item in cart:
                item_name = cart_item.get('name')
                quantity = cart_item.get('quantity', 0)
                price = cart_item.get('price', 0)  # Use price from cart directly

                # Get category from grocery data
                category = 'Uncategorized'
                if item_name and item_name.lower() in grocery_lookup:
                    category = grocery_lookup[item_name.lower()].get('category', 'Uncategorized')

                # Add item to order with category information
                items.append({
                    'name': item_name,
                    'price': price,
                    'quantity': quantity,
                    'category': category
                })
                total += price * quantity
            
            # Create order object
            import datetime
            import uuid
            
            order = {
                'order_id': str(uuid.uuid4())[:8],
                'date': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'name': name,
                'email': email,
                'phone': phone,
                'address': address,
                'items': items,
                'total': total,
                'status': 'pending',  # Default status for new orders
                'status_updated_at': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'status_history': [{
                    'from_status': '',
                    'to_status': 'pending',
                    'changed_at': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'changed_by': 'system',
                    'notes': 'Order placed by customer'
                }]
            }
            
            # Create user account for guest customers (if they don't exist)
            if not current_user.is_authenticated:
                existing_user = User.query.filter_by(email=email).first()
                if not existing_user:
                    try:
                        # Create a new user account
                        new_user = User(
                            username=email.split('@')[0],  # Use email prefix as username
                            email=email,
                            password='defaultpassword123',  # Default password - user can change later
                            first_name=name.split()[0] if name.split() else 'Customer',
                            last_name=' '.join(name.split()[1:]) if len(name.split()) > 1 else '',
                            phone=phone,
                            address=address,
                            role='customer'
                        )
                        db.session.add(new_user)
                        db.session.commit()

                        app.logger.info(f"Created new user account for guest customer: {email}")
                        flash(f"Account created! You can login with email {email} and password 'defaultpassword123' to track your orders.", "info")

                    except Exception as e:
                        app.logger.error(f"Error creating user account: {str(e)}")
                        # Continue with order processing even if user creation fails

            # Save order to orders.json
            orders = []
            try:
                if os.path.exists('orders.json'):
                    with open('orders.json', 'r') as f:
                        orders = json.load(f)
            except Exception as e:
                app.logger.error(f"Error loading orders: {str(e)}")

            orders.append(order)

            try:
                with open('orders.json', 'w') as f:
                    json.dump(orders, f, indent=4)
                app.logger.info(f"Order {order['order_id']} saved successfully for {email}")
            except Exception as e:
                app.logger.error(f"Error saving order: {str(e)}")
                flash("There was an error processing your order. Please try again.")
                return redirect(url_for('checkout'))
            
            # Store order in session for payment processing (don't save to file yet)
            session['pending_order'] = order

            # Clear cart
            session['cart'] = []

            # Redirect to payment selection
            flash(f"Order details confirmed! Order ID: {order['order_id']}")
            return redirect(url_for('payment_selection'))
            
        except Exception as e:
            app.logger.error(f"Error in checkout: {str(e)}")
            flash(f"Error processing checkout: {str(e)}")
            return redirect(url_for('checkout'))
    
    # GET request - show checkout form
    cart = session.get('cart', [])
    total = sum(item.get('price', 0) * item.get('quantity', 0) for item in cart)
    
    return render_template('checkout.html', cart=cart, total=total)


@app.route('/payment-selection')
def payment_selection():
    """Payment method selection page"""
    if 'pending_order' not in session:
        flash("No pending order found. Please place an order first.")
        return redirect(url_for('shop'))

    try:
        # Get payment methods from database
        from models import PaymentMethod
        payment_methods = PaymentMethod.query.filter_by(is_active=True).order_by(PaymentMethod.sort_order).all()

        order = session['pending_order']

        return render_template('payment_selection.html',
                             order=order,
                             payment_methods=payment_methods)

    except Exception as e:
        app.logger.error(f"Error in payment_selection: {str(e)}")
        flash(f"Error loading payment methods: {str(e)}")
        return redirect(url_for('checkout'))


@app.route('/process-payment', methods=['POST'])
def process_payment():
    """Process the selected payment method"""
    if 'pending_order' not in session:
        flash("No pending order found. Please place an order first.")
        return redirect(url_for('shop'))

    try:
        # Get form data
        order_id = request.form.get('order_id')
        amount = float(request.form.get('amount', 0))
        payment_method = request.form.get('payment_method')
        payment_provider = request.form.get('payment_provider')

        order = session['pending_order']

        # Validate order
        if order.get('order_id') != order_id:
            flash("Order validation failed. Please try again.")
            return redirect(url_for('payment_selection'))

        if payment_method == 'upi':
            return process_upi_payment(order, payment_provider, amount)
        elif payment_method == 'cod':
            return process_cod_payment(order, amount)
        else:
            flash("Invalid payment method selected.")
            return redirect(url_for('payment_selection'))

    except Exception as e:
        app.logger.error(f"Error in process_payment: {str(e)}")
        flash(f"Error processing payment: {str(e)}")
        return redirect(url_for('payment_selection'))


def process_upi_payment(order, payment_provider, amount):
    """Process UPI payment"""
    try:
        import uuid
        from models import Payment

        # Generate payment ID
        payment_id = f"PAY_{str(uuid.uuid4())[:8].upper()}"

        # Create payment record
        payment = Payment(
            payment_id=payment_id,
            order_id=order['order_id'],
            user_id=current_user.id if current_user.is_authenticated else None,
            amount=amount,
            currency='INR',
            payment_method='upi',
            payment_provider=payment_provider,
            status='pending',
            customer_name=order.get('name'),
            customer_email=order.get('email'),
            customer_phone=order.get('phone')
        )

        db.session.add(payment)
        db.session.commit()

        # Store payment info in session
        session['current_payment'] = {
            'payment_id': payment_id,
            'order_id': order['order_id'],
            'amount': amount,
            'provider': payment_provider
        }

        # Redirect to UPI payment page
        return redirect(url_for('upi_payment', payment_id=payment_id))

    except Exception as e:
        app.logger.error(f"Error in process_upi_payment: {str(e)}")
        flash(f"Error processing UPI payment: {str(e)}")
        return redirect(url_for('payment_selection'))


def process_cod_payment(order, amount):
    """Process Cash on Delivery payment"""
    try:
        import uuid
        from models import Payment

        # Generate payment ID
        payment_id = f"COD_{str(uuid.uuid4())[:8].upper()}"

        # Create payment record
        payment = Payment(
            payment_id=payment_id,
            order_id=order['order_id'],
            user_id=current_user.id if current_user.is_authenticated else None,
            amount=amount,
            currency='INR',
            payment_method='cod',
            payment_provider='cod',
            status='confirmed',  # COD is confirmed immediately
            customer_name=order.get('name'),
            customer_email=order.get('email'),
            customer_phone=order.get('phone')
        )

        db.session.add(payment)
        db.session.commit()

        # Save order to orders.json now that payment is confirmed
        orders = []
        try:
            if os.path.exists('orders.json'):
                with open('orders.json', 'r') as f:
                    orders = json.load(f)
        except Exception as e:
            app.logger.error(f"Error loading orders: {str(e)}")

        # Add payment info to order
        order['payment_id'] = payment_id
        order['payment_method'] = 'cod'
        order['payment_status'] = 'confirmed'

        orders.append(order)

        try:
            with open('orders.json', 'w') as f:
                json.dump(orders, f, indent=4)
            app.logger.info(f"Order {order['order_id']} saved with COD payment")
        except Exception as e:
            app.logger.error(f"Error saving order: {str(e)}")
            flash("There was an error saving your order. Please contact support.")
            return redirect(url_for('payment_selection'))

        # Clear pending order from session
        session.pop('pending_order', None)

        flash(f"Order placed successfully with Cash on Delivery! Order ID: {order['order_id']}")
        return render_template('order_confirmation.html', order=order, payment=payment.to_dict())

    except Exception as e:
        app.logger.error(f"Error in process_cod_payment: {str(e)}")
        flash(f"Error processing COD payment: {str(e)}")
        return redirect(url_for('payment_selection'))


@app.route('/upi-payment/<payment_id>')
def upi_payment(payment_id):
    """UPI payment page"""
    try:
        from models import Payment

        payment = Payment.query.filter_by(payment_id=payment_id).first()
        if not payment:
            flash("Payment not found.")
            return redirect(url_for('shop'))

        if payment.status != 'pending':
            flash("This payment has already been processed.")
            return redirect(url_for('shop'))

        return render_template('upi_payment.html',
                             payment_id=payment_id,
                             order_id=payment.order_id,
                             amount=float(payment.amount),
                             payment_provider=payment.payment_provider)

    except Exception as e:
        app.logger.error(f"Error in upi_payment: {str(e)}")
        flash(f"Error loading payment page: {str(e)}")
        return redirect(url_for('shop'))


@app.route('/check-payment-status/<payment_id>')
def check_payment_status(payment_id):
    """Check payment status (AJAX endpoint)"""
    try:
        from models import Payment

        payment = Payment.query.filter_by(payment_id=payment_id).first()
        if not payment:
            return {'status': 'not_found'}, 404

        return {
            'status': payment.status,
            'payment_id': payment_id,
            'transaction_id': payment.transaction_id
        }

    except Exception as e:
        app.logger.error(f"Error checking payment status: {str(e)}")
        return {'status': 'error', 'message': str(e)}, 500


@app.route('/verify-payment/<payment_id>', methods=['POST'])
def verify_payment(payment_id):
    """Verify payment manually (for demo purposes)"""
    try:
        from models import Payment
        import datetime

        payment = Payment.query.filter_by(payment_id=payment_id).first()
        if not payment:
            return {'success': False, 'message': 'Payment not found'}, 404

        if payment.status != 'pending':
            return {'success': False, 'message': 'Payment already processed'}, 400

        # For demo purposes, we'll simulate successful payment
        # In real implementation, this would verify with actual UPI gateway

        payment.status = 'completed'
        payment.completed_at = datetime.datetime.utcnow()
        payment.transaction_id = f"UPI_{payment_id}_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"

        db.session.commit()

        # Save order to orders.json now that payment is confirmed
        if 'pending_order' in session:
            order = session['pending_order']

            orders = []
            try:
                if os.path.exists('orders.json'):
                    with open('orders.json', 'r') as f:
                        orders = json.load(f)
            except Exception as e:
                app.logger.error(f"Error loading orders: {str(e)}")

            # Add payment info to order
            order['payment_id'] = payment_id
            order['payment_method'] = 'upi'
            order['payment_status'] = 'completed'
            order['transaction_id'] = payment.transaction_id

            orders.append(order)

            try:
                with open('orders.json', 'w') as f:
                    json.dump(orders, f, indent=4)
                app.logger.info(f"Order {order['order_id']} saved with UPI payment")
            except Exception as e:
                app.logger.error(f"Error saving order: {str(e)}")
                return {'success': False, 'message': 'Error saving order'}, 500

            # Clear pending order from session
            session.pop('pending_order', None)

        return {'success': True, 'message': 'Payment verified successfully'}

    except Exception as e:
        app.logger.error(f"Error verifying payment: {str(e)}")
        return {'success': False, 'message': str(e)}, 500


@app.route('/payment-success/<payment_id>')
def payment_success(payment_id):
    """Payment success page"""
    try:
        from models import Payment

        payment = Payment.query.filter_by(payment_id=payment_id).first()
        if not payment:
            flash("Payment not found.")
            return redirect(url_for('shop'))

        if payment.status != 'completed':
            flash("Payment not completed.")
            return redirect(url_for('shop'))

        # Get order details from orders.json
        orders = []
        try:
            if os.path.exists('orders.json'):
                with open('orders.json', 'r') as f:
                    orders = json.load(f)
        except Exception as e:
            app.logger.error(f"Error loading orders: {str(e)}")

        order = None
        for o in orders:
            if o.get('order_id') == payment.order_id:
                order = o
                break

        if not order:
            flash("Order not found.")
            return redirect(url_for('shop'))

        return render_template('order_confirmation.html',
                             order=order,
                             payment=payment.to_dict())

    except Exception as e:
        app.logger.error(f"Error in payment_success: {str(e)}")
        flash(f"Error loading success page: {str(e)}")
        return redirect(url_for('shop'))

@app.route('/order_confirmation/<order_id>')
def order_confirmation(order_id):
    try:
        orders = load_orders()
        order = next((o for o in orders if o.get('order_id') == order_id), None)
        
        if not order:
            flash("Order not found!")
            return redirect(url_for('shop'))
        
        return render_template('order_confirmation.html', order=order)
    except Exception as e:
        app.logger.error(f"Error in order_confirmation: {str(e)}")
        flash(f"Error displaying order: {str(e)}")
        return redirect(url_for('shop'))

@app.route('/admin')
def admin_redirect():
    return redirect(url_for('admin_login'))

@app.route('/admin/login', methods=['GET', 'POST'])
@rate_limit(max_requests=20, window_minutes=15)  # Same generous limits as user login
def admin_login():
    try:
        if request.method == 'POST':
            username = request.form.get('username')
            password = request.form.get('password')
            
            if username == 'admin' and password == 'admin123':
                session['admin_logged_in'] = True
                flash("Welcome, Admin!")
                return redirect(url_for('admin_dashboard'))
            else:
                flash("Invalid credentials!")
        
        return render_template('admin_login.html')
    except Exception as e:
        app.logger.error(f"Error in admin_login: {str(e)}")
        flash(f"Error during login: {str(e)}")
        return render_template('admin_login.html')

@app.route('/admin/logout')
def admin_logout():
    session.pop('admin_logged_in', None)
    flash("You have been logged out.")
    return redirect(url_for('admin_login'))

@app.route('/admin/dashboard')
def admin_dashboard():
    """Legacy admin dashboard - session-based authentication"""
    # Check if using legacy admin session
    if session.get('admin_logged_in'):
        try:
            # Get basic statistics for legacy admin
            orders = load_orders()
            items = load_grocery_data()

            # Calculate order counts by status
            pending_count = len([o for o in orders if o.get('status') == 'pending'])
            confirmed_count = len([o for o in orders if o.get('status') == 'confirmed'])
            delivered_count = len([o for o in orders if o.get('status') == 'delivered'])

            return render_template('admin_dashboard.html',
                                 orders=orders[:10],
                                 items=items[:10],
                                 pending_count=pending_count,
                                 confirmed_count=confirmed_count,
                                 delivered_count=delivered_count)
        except Exception as e:
            app.logger.error(f"Error in legacy admin_dashboard: {str(e)}")
            flash(f"Error loading dashboard: {str(e)}")
            return redirect(url_for('admin_login'))

    # Check if using new Flask-Login admin system
    elif current_user.is_authenticated and current_user.is_admin():
        # Redirect to new admin dashboard
        return redirect(url_for('admin_dashboard_new'))

    # Not authenticated with either system
    else:
        flash("Please login first!")
        return redirect(url_for('admin_login'))



@app.route('/admin/update_order/<order_id>', methods=['POST'])
@admin_required
def update_order_status(order_id):
    """Update order status with advanced tracking and history"""
    try:
        status = request.form.get('status')
        if not status:
            flash("No status provided!")
            return redirect(url_for('admin_dashboard'))

        orders = load_orders()
        order = next((o for o in orders if o.get('order_id') == order_id), None)

        if not order:
            flash("Order not found!")
            return redirect(url_for('admin_dashboard'))

        # Store old status for tracking
        old_status = order.get('status', 'unknown')

        # Update status
        order['status'] = status

        # Add status change timestamp
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        order['status_updated_at'] = timestamp

        # Initialize status history if it doesn't exist
        if 'status_history' not in order:
            order['status_history'] = []

        # Record status change in history
        status_change = {
            'from_status': old_status,
            'to_status': status,
            'changed_at': timestamp,
            'changed_by': 'admin',  # In future, use actual admin username
            'notes': request.form.get('notes', '')  # Optional notes from admin
        }
        order['status_history'].append(status_change)

        # Set specific timestamps based on status
        if status == 'confirmed' and 'confirmed_at' not in order:
            order['confirmed_at'] = timestamp
        elif status == 'processing' and 'processing_at' not in order:
            order['processing_at'] = timestamp
        elif status == 'shipped' and 'shipped_at' not in order:
            order['shipped_at'] = timestamp
        elif status == 'delivered' and 'delivered_at' not in order:
            order['delivered_at'] = timestamp

        # Calculate processing time for analytics
        if status == 'delivered' and order.get('date'):
            try:
                order_date = datetime.strptime(order['date'], '%Y-%m-%d %H:%M:%S')
                delivery_date = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')
                processing_time = (delivery_date - order_date).total_seconds() / 3600  # hours
                order['processing_time_hours'] = round(processing_time, 2)
            except:
                pass  # Skip if date parsing fails

        save_orders(orders)

        flash(f"Order {order_id[:8]}... status updated from '{old_status}' to '{status}'!", "success")

        # Redirect back to order detail if came from there
        if request.referrer and 'admin/order/' in request.referrer:
            return redirect(url_for('admin_order_detail', order_id=order_id))

        return redirect(url_for('admin_dashboard'))
    except Exception as e:
        app.logger.error(f"Error in update_order_status: {str(e)}")
        flash(f"Error updating order: {str(e)}", "danger")
        return redirect(url_for('admin_dashboard'))

@app.route('/admin/bulk_order_operations', methods=['POST'])
@admin_required
def bulk_order_operations():
    """Handle bulk operations on multiple orders"""
    try:
        action = request.form.get('bulk_action')
        selected_orders = request.form.getlist('selected_orders')

        if not action or not selected_orders:
            flash("Please select an action and at least one order!", "warning")
            return redirect(url_for('admin_dashboard'))

        orders = load_orders()
        updated_count = 0

        for order in orders:
            if order.get('order_id') in selected_orders:
                if action == 'mark_confirmed':
                    if order.get('status') != 'confirmed':
                        order['status'] = 'confirmed'
                        order['confirmed_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        updated_count += 1

                elif action == 'mark_shipped':
                    if order.get('status') in ['confirmed', 'processing']:
                        order['status'] = 'shipped'
                        order['shipped_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        updated_count += 1

                elif action == 'mark_delivered':
                    if order.get('status') == 'shipped':
                        order['status'] = 'delivered'
                        order['delivered_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        updated_count += 1

                elif action == 'cancel_orders':
                    if order.get('status') not in ['delivered', 'cancelled']:
                        order['status'] = 'cancelled'
                        order['cancelled_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        updated_count += 1

        if updated_count > 0:
            save_orders(orders)
            flash(f"Successfully updated {updated_count} order(s) with action: {action.replace('_', ' ').title()}!", "success")
        else:
            flash("No orders were updated. Check order statuses and try again.", "info")

        return redirect(url_for('admin_dashboard'))

    except Exception as e:
        app.logger.error(f"Error in bulk_order_operations: {str(e)}")
        flash(f"Error performing bulk operation: {str(e)}", "danger")
        return redirect(url_for('admin_dashboard'))

@app.route('/admin/order_analytics')
@admin_required
def order_analytics():
    """Display order analytics and statistics"""
    try:
        orders = load_orders()

        # Calculate analytics
        analytics = {
            'total_orders': len(orders),
            'pending_orders': len([o for o in orders if o.get('status') == 'pending']),
            'confirmed_orders': len([o for o in orders if o.get('status') == 'confirmed']),
            'shipped_orders': len([o for o in orders if o.get('status') == 'shipped']),
            'delivered_orders': len([o for o in orders if o.get('status') == 'delivered']),
            'cancelled_orders': len([o for o in orders if o.get('status') == 'cancelled']),
            'total_revenue': sum(float(o.get('total', 0)) for o in orders if o.get('status') != 'cancelled'),
            'average_order_value': 0,
            'orders_by_status': {},
            'recent_orders': sorted(orders, key=lambda x: x.get('date', ''), reverse=True)[:10]
        }

        # Calculate average order value
        if analytics['total_orders'] > 0:
            analytics['average_order_value'] = analytics['total_revenue'] / analytics['total_orders']

        # Group orders by status for charts
        for order in orders:
            status = order.get('status', 'unknown')
            analytics['orders_by_status'][status] = analytics['orders_by_status'].get(status, 0) + 1

        return render_template('admin_order_analytics.html', analytics=analytics)

    except Exception as e:
        app.logger.error(f"Error in order_analytics: {str(e)}")
        flash(f"Error loading analytics: {str(e)}", "danger")
        return redirect(url_for('admin_dashboard'))

@app.route('/admin/order/<order_id>')
@admin_required
def admin_order_detail(order_id):
    """View detailed order information"""
    try:
        orders = load_orders()
        order = next((o for o in orders if o.get('order_id') == order_id), None)

        if not order:
            flash("Order not found!")
            return redirect(url_for('admin_dashboard'))

        # Enhance order items with category information from grocery_data
        grocery_items = load_grocery_data()
        grocery_lookup = {item.get('name', '').lower(): item for item in grocery_items}

        # Add categories to order items if missing
        if order.get('items'):
            for item in order['items']:
                if not item.get('category') or item.get('category') == 'Uncategorized':
                    item_name = item.get('name', '').lower()
                    if item_name in grocery_lookup:
                        item['category'] = grocery_lookup[item_name].get('category', 'Uncategorized')
                    else:
                        # Try partial matching for items like "Organic Bananas" vs "Banana"
                        for grocery_name, grocery_item in grocery_lookup.items():
                            if grocery_name in item_name or item_name in grocery_name:
                                item['category'] = grocery_item.get('category', 'Uncategorized')
                                break

        return render_template('admin_order_detail.html', order=order)
    except Exception as e:
        app.logger.error(f"Error in admin_order_detail: {str(e)}")
        flash(f"Error displaying order details: {str(e)}")
        return redirect(url_for('admin_dashboard'))

@app.route('/admin/inventory')
@admin_required
def admin_inventory():
    try:
        items = load_grocery_data()
        
        # Ensure all items have the required fields
        for item in items:
            if 'image' not in item:
                item['image'] = 'default.jpg'
            if 'in_stock' not in item:
                item['in_stock'] = item.get('quantity', 0) > 0
                
        return render_template('admin_inventory.html', items=items)
    except Exception as e:
        app.logger.error(f"Error in admin_inventory: {str(e)}")
        flash(f"Error loading inventory: {str(e)}", "danger")
        return redirect(url_for('admin_dashboard'))

@app.route('/admin/add_item', methods=['GET', 'POST'])
@admin_required
def admin_add_item():
    try:
        if request.method == 'POST':
            name = request.form.get('name')
            price = float(request.form.get('price', 0))
            quantity = int(request.form.get('quantity', 0))
            category = request.form.get('category')
            description = request.form.get('description', '')
            image = request.form.get('image', 'default.jpg')
            
            if not name or price <= 0 or quantity < 0:
                flash("Please provide valid item details!")
                return render_template('admin_add_item.html')
            
            items = load_grocery_data()
            
            # Check if item already exists
            if any(i['name'] == name for i in items):
                flash(f"Item '{name}' already exists!")
                return render_template('admin_add_item.html')
            
            # Add new item
            items.append({
                'name': name,
                'price': price,
                'quantity': quantity,
                'category': category,
                'description': description,
                'image': image,
                'in_stock': quantity > 0
            })
            
            save_grocery_data(items)
            flash(f"Item '{name}' added successfully!")
            return redirect(url_for('admin_inventory'))
        
        return render_template('admin_add_item.html')
    except ValueError:
        flash("Please enter valid numbers for price and quantity!")
        return render_template('admin_add_item.html')
    except Exception as e:
        app.logger.error(f"Error in admin_add_item: {str(e)}")
        flash(f"Error adding item: {str(e)}")
        return redirect(url_for('admin_inventory'))

@app.route('/admin/edit_item/<item_name>', methods=['GET', 'POST'])
@admin_required
def admin_edit_item(item_name):
    try:
        items = load_grocery_data()
        item = next((i for i in items if i.get('name') == item_name), None)
        
        if not item:
            flash(f"Item '{item_name}' not found!")
            return redirect(url_for('admin_inventory'))
        
        if request.method == 'POST':
            price = float(request.form.get('price', 0))
            quantity = int(request.form.get('quantity', 0))
            category = request.form.get('category')
            description = request.form.get('description', '')
            image = request.form.get('image', 'default.jpg')
            
            if price <= 0 or quantity < 0:
                flash("Please provide valid item details!")
                return render_template('admin_edit_item.html', item=item)
            
            # Update item
            item['price'] = price
            item['quantity'] = quantity
            item['category'] = category
            item['description'] = description
            item['image'] = image
            item['in_stock'] = quantity > 0
            
            save_grocery_data(items)
            flash(f"Item '{item_name}' updated successfully!")
            return redirect(url_for('admin_inventory'))
        
        return render_template('admin_edit_item.html', item=item)
    except ValueError:
        flash("Please enter valid numbers for price and quantity!")
        return render_template('admin_edit_item.html', item=item)
    except Exception as e:
        app.logger.error(f"Error in admin_edit_item: {str(e)}")
        flash(f"Error editing item: {str(e)}")
        return redirect(url_for('admin_inventory'))

@app.route('/admin/delete_item/<item_name>')
@admin_required
def admin_delete_item(item_name):
    try:
        items = load_grocery_data()
        items = [i for i in items if i['name'] != item_name]
        save_grocery_data(items)
        flash(f"Item '{item_name}' deleted successfully!")
        return redirect(url_for('admin_inventory'))
    except Exception as e:
        app.logger.error(f"Error in admin_delete_item: {str(e)}")
        flash(f"Error deleting item: {str(e)}")
        return redirect(url_for('admin_inventory'))

@app.route('/admin/update_item/<item_name>', methods=['POST'])
@admin_required
def admin_update_item(item_name):
    try:
        items = load_grocery_data()
        item = next((i for i in items if i['name'] == item_name), None)
        
        if not item:
            flash(f"Item '{item_name}' not found!")
            return redirect(url_for('admin_inventory'))
        
        quantity = int(request.form.get('quantity', 0))
        price = float(request.form.get('price', 0))
        
        if quantity < 0 or price <= 0:
            flash("Please provide valid values!")
            return redirect(url_for('admin_inventory'))
        
        # Update item
        item['quantity'] = quantity
        item['price'] = price
        item['in_stock'] = quantity > 0
        
        save_grocery_data(items)
        flash(f"Item '{item_name}' updated successfully!")
        return redirect(url_for('admin_inventory'))
    except ValueError:
        flash("Please enter valid numbers for price and quantity!")
        return redirect(url_for('admin_inventory'))
    except Exception as e:
        app.logger.error(f"Error in admin_update_item: {str(e)}")
        flash(f"Error updating item: {str(e)}")
        return redirect(url_for('admin_inventory'))

# Debug routes
@app.route('/debug/orders')
def debug_orders():
    if not app.debug:
        return "Debug mode is disabled", 403
    
    try:
        orders = load_orders()
        return render_template('debug_orders.html', orders=orders)
    except Exception as e:
        app.logger.error(f"Error in debug_orders: {str(e)}")
        flash(f"Error debugging orders: {str(e)}")
        return redirect(url_for('home'))

@app.route('/debug/clear_orders')
def debug_clear_orders():
    if not app.debug:
        return "Debug mode is disabled", 403
    
    try:
        save_orders([])
        flash("Orders cleared for debugging")
        return redirect(url_for('debug_orders'))
    except Exception as e:
        app.logger.error(f"Error in debug_clear_orders: {str(e)}")
        flash(f"Error clearing orders: {str(e)}")
        return redirect(url_for('debug_orders'))

@app.route('/debug/fix_orders')
def debug_fix_orders():
    if not app.debug:
        return "Debug mode is disabled", 403

    try:
        # Create a new empty orders file
        save_orders([])
        flash("Orders file has been reset")
        return redirect(url_for('admin_dashboard'))
    except Exception as e:
        app.logger.error(f"Error in debug_fix_orders: {str(e)}")
        flash(f"Error fixing orders: {str(e)}")
        return redirect(url_for('admin_login'))

@app.route('/debug/clear_rate_limits')
def debug_clear_rate_limits():
    """Clear all rate limits for development"""
    if not app.debug:
        return "Debug mode is disabled", 403

    try:
        global rate_limit_storage
        rate_limit_storage.clear()
        flash("Rate limits cleared successfully!")
        return redirect(url_for('home'))
    except Exception as e:
        app.logger.error(f"Error clearing rate limits: {str(e)}")
        flash(f"Error clearing rate limits: {str(e)}")
        return redirect(url_for('home'))

@app.route('/debug/admin_orders')
def debug_admin_orders():
    """Debug route to check admin orders data"""
    try:
        orders = load_orders()
        debug_info = {
            'total_orders': len(orders),
            'orders': []
        }

        for order in orders:
            order_debug = {
                'order_id': order.get('order_id', 'N/A'),
                'date': order.get('date', 'N/A'),
                'name': order.get('name', 'N/A'),
                'items_count': len(order.get('items', [])),
                'items': order.get('items', []),
                'total': order.get('total', 0),
                'status': order.get('status', 'N/A')
            }
            debug_info['orders'].append(order_debug)

        return f"""
        <h2>Admin Orders Debug</h2>
        <p><strong>Total Orders:</strong> {debug_info['total_orders']}</p>
        <hr>
        {''.join([f'''
        <div style="border: 1px solid #ccc; margin: 10px; padding: 10px;">
            <h3>Order {order['order_id'][:8]}...</h3>
            <p><strong>Date:</strong> {order['date']}</p>
            <p><strong>Customer:</strong> {order['name']}</p>
            <p><strong>Items Count:</strong> {order['items_count']}</p>
            <p><strong>Items:</strong> {order['items']}</p>
            <p><strong>Total:</strong> ${order['total']:.2f}</p>
            <p><strong>Status:</strong> {order['status']}</p>
        </div>
        ''' for order in debug_info['orders']])}
        <hr>
        <a href="/admin/dashboard">Back to Admin Dashboard</a>
        """
    except Exception as e:
        return f"Error: {str(e)}"

# Error handlers
@app.errorhandler(404)
def page_not_found(e):
    return render_template('error.html', error=e, title="Page Not Found"), 404

@app.errorhandler(500)
def server_error(e):
    return render_template('error.html', error=e, title="Server Error"), 500

@app.errorhandler(Exception)
def handle_exception(e):
    # Pass through HTTP errors
    if isinstance(e, HTTPException):
        return e

    # Now you're handling non-HTTP exceptions only
    app.logger.error(f"Unhandled exception: {str(e)}")
    return render_template('error.html',
                          error="An unexpected error occurred. Please try again later.",
                          title="Unexpected Error"), 500


# ===== AUTHENTICATION ROUTES =====

@app.route('/login', methods=['GET', 'POST'])
@rate_limit(max_requests=20, window_minutes=15)  # Increased from 5 to 20 for better usability
def login():
    """User login route"""
    if current_user.is_authenticated:
        return redirect(url_for('home'))

    form = LoginForm()
    if form.validate_on_submit():
        # Check if login is username or email
        user = User.query.filter(
            (User.username == form.username.data) |
            (User.email == form.username.data)
        ).first()

        if user and user.check_password(form.password.data):
            if not user.is_active:
                flash('Your account has been deactivated. Please contact support.', 'error')
                return redirect(url_for('login'))

            login_user(user, remember=form.remember_me.data)
            user.update_last_login()

            flash(f'Welcome back, {user.first_name}!', 'success')

            # Redirect to next page or home
            next_page = request.args.get('next')
            if not next_page:
                next_page = url_for('home')
            else:
                # Simple security check for next parameter
                try:
                    parsed = url_parse(next_page)
                    if hasattr(parsed, 'netloc'):
                        if parsed.netloc != '':
                            next_page = url_for('home')
                    else:
                        # For urllib.parse.urlparse
                        if parsed.netloc != '':
                            next_page = url_for('home')
                except:
                    next_page = url_for('home')
            return redirect(next_page)
        else:
            flash('Invalid username/email or password.', 'error')

    return render_template('auth/login.html', title='Sign In', form=form)


@app.route('/register', methods=['GET', 'POST'])
@rate_limit(max_requests=10, window_minutes=60)  # Increased from 3 to 10 for better usability
def register():
    """User registration route"""
    if current_user.is_authenticated:
        return redirect(url_for('home'))

    form = RegistrationForm()
    if form.validate_on_submit():
        user = User(
            username=form.username.data,
            email=form.email.data,
            password=form.password.data,
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            phone=form.phone.data
        )

        db.session.add(user)
        db.session.commit()

        flash(f'Registration successful! Welcome to Fresh Grocery, {user.first_name}!', 'success')
        login_user(user)
        return redirect(url_for('home'))

    return render_template('auth/register.html', title='Create Account', form=form)


@app.route('/logout')
@login_required
def logout():
    """User logout route"""
    logout_user()
    flash('You have been logged out successfully.', 'info')
    return redirect(url_for('home'))


@app.route('/profile')
@login_required
def profile():
    """User profile page with order tracking"""
    try:
        # Get customer orders from JSON file (matching by email)
        orders = load_orders()
        customer_orders = []

        # Filter orders by customer email
        for order in orders:
            if order.get('email') == current_user.email:
                customer_orders.append(order)

        # Sort orders by date (newest first)
        customer_orders.sort(key=lambda x: x.get('date', ''), reverse=True)

        # Calculate order statistics
        order_stats = {
            'total_orders': len(customer_orders),
            'pending_orders': len([o for o in customer_orders if o.get('status') == 'pending']),
            'delivered_orders': len([o for o in customer_orders if o.get('status') == 'delivered']),
            'total_spent': sum(float(o.get('total', 0)) for o in customer_orders if o.get('status') != 'cancelled')
        }

        return render_template('auth/profile.html',
                             title='My Profile',
                             user=current_user,
                             customer_orders=customer_orders,
                             order_stats=order_stats)
    except Exception as e:
        app.logger.error(f"Error in profile: {str(e)}")
        flash(f"Error loading profile: {str(e)}", "danger")
        return render_template('auth/profile.html',
                             title='My Profile',
                             user=current_user,
                             customer_orders=[],
                             order_stats={'total_orders': 0, 'pending_orders': 0, 'delivered_orders': 0, 'total_spent': 0})


@app.route('/profile/edit', methods=['GET', 'POST'])
@login_required
def edit_profile():
    """Edit user profile"""
    form = ProfileForm(current_user.email)

    if form.validate_on_submit():
        current_user.first_name = form.first_name.data
        current_user.last_name = form.last_name.data
        current_user.email = form.email.data
        current_user.phone = form.phone.data
        current_user.address = form.address.data
        current_user.city = form.city.data
        current_user.state = form.state.data
        current_user.zip_code = form.zip_code.data

        db.session.commit()
        flash('Your profile has been updated successfully!', 'success')
        return redirect(url_for('profile'))

    elif request.method == 'GET':
        form.first_name.data = current_user.first_name
        form.last_name.data = current_user.last_name
        form.email.data = current_user.email
        form.phone.data = current_user.phone
        form.address.data = current_user.address
        form.city.data = current_user.city
        form.state.data = current_user.state
        form.zip_code.data = current_user.zip_code

    return render_template('auth/edit_profile.html', title='Edit Profile', form=form)


@app.route('/my-orders')
@login_required
def my_orders():
    """Customer order history and tracking page"""
    try:
        # Get customer orders from JSON file (matching by email)
        orders = load_orders()
        customer_orders = []

        # Filter orders by customer email
        for order in orders:
            if order.get('email') == current_user.email:
                customer_orders.append(order)

        # Sort orders by date (newest first)
        customer_orders.sort(key=lambda x: x.get('date', ''), reverse=True)

        # Calculate order statistics
        order_stats = {
            'total_orders': len(customer_orders),
            'pending_orders': len([o for o in customer_orders if o.get('status') == 'pending']),
            'confirmed_orders': len([o for o in customer_orders if o.get('status') == 'confirmed']),
            'shipped_orders': len([o for o in customer_orders if o.get('status') == 'shipped']),
            'delivered_orders': len([o for o in customer_orders if o.get('status') == 'delivered']),
            'cancelled_orders': len([o for o in customer_orders if o.get('status') == 'cancelled']),
            'total_spent': sum(float(o.get('total', 0)) for o in customer_orders if o.get('status') != 'cancelled'),
            'average_order_value': 0
        }

        # Calculate average order value
        if order_stats['total_orders'] > 0:
            order_stats['average_order_value'] = order_stats['total_spent'] / order_stats['total_orders']

        return render_template('customer_orders.html',
                             title='My Orders',
                             customer_orders=customer_orders,
                             order_stats=order_stats)
    except Exception as e:
        app.logger.error(f"Error in my_orders: {str(e)}")
        flash(f"Error loading orders: {str(e)}", "danger")
        return redirect(url_for('profile'))




@app.route('/debug-user')
@login_required
def debug_user():
    """Debug current user info"""
    orders = load_orders()
    customer_orders = [o for o in orders if o.get('email') == current_user.email]

    debug_info = {
        'current_user_email': current_user.email,
        'current_user_name': f"{current_user.first_name} {current_user.last_name}",
        'total_orders_in_system': len(orders),
        'customer_orders_count': len(customer_orders),
        'all_order_emails': [o.get('email') for o in orders],
        'customer_orders': customer_orders
    }

    correct_user = current_user.email == '<EMAIL>'

    return f"""
    <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px;">
        <h2>🔍 Debug User Information</h2>
        <div style="background: {'#d4edda' if correct_user else '#f8d7da'}; padding: 15px; border-radius: 5px; margin: 10px 0;">
            <p><strong>Current User Email:</strong> {debug_info['current_user_email']}</p>
            <p><strong>Current User Name:</strong> {debug_info['current_user_name']}</p>
            <p><strong>Status:</strong> {'✅ CORRECT USER' if correct_user else '❌ WRONG USER'}</p>
        </div>

        <div style="background: #e9ecef; padding: 15px; border-radius: 5px; margin: 10px 0;">
            <p><strong>Total Orders in System:</strong> {debug_info['total_orders_in_system']}</p>
            <p><strong>Customer Orders Found:</strong> {debug_info['customer_orders_count']}</p>
            <p><strong>All Order Emails:</strong> {', '.join(set(debug_info['all_order_emails']))}</p>
        </div>

        {'<div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;"><h3>⚠️ You need to <NAME_EMAIL> to see orders!</h3><p>Password: password123</p><a href="/quick-login" style="background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-top: 10px; display: inline-block;">🚀 Quick Login as Test User</a></div>' if not correct_user else ''}

        <h3>Customer Orders:</h3>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
            {'<br>'.join([f"Order #{o.get('order_id', 'N/A')[:8]} - {o.get('date')} - ${o.get('total', 0)}" for o in debug_info['customer_orders']]) if debug_info['customer_orders'] else 'No orders found for this user'}
        </div>

        <div style="margin: 20px 0;">
            <a href="/logout" style="background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;">Logout</a>
            <a href="/my-orders" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;">My Orders</a>
            <a href="/profile" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Profile</a>
        </div>
    </div>
    """

@app.route('/quick-login')
def quick_login():
    """Quick login for testing purposes"""
    try:
        # Logout current user
        if current_user.is_authenticated:
            logout_user()

        # Login as test user
        test_user = User.query.filter_by(email='<EMAIL>').first()
        if test_user:
            login_user(test_user)
            flash('Logged in as test user successfully!', 'success')
            return redirect(url_for('my_orders'))
        else:
            flash('Test user not found!', 'danger')
            return redirect(url_for('login'))
    except Exception as e:
        app.logger.error(f"Error in quick_login: {str(e)}")
        flash(f"Error during quick login: {str(e)}", 'danger')
        return redirect(url_for('login'))

@app.route('/track-order/<order_id>')
@login_required
def track_order(order_id):
    """Customer order tracking detail page"""
    try:
        orders = load_orders()
        order = None

        # Find order by ID and verify it belongs to current user
        for o in orders:
            if o.get('order_id') == order_id and o.get('email') == current_user.email:
                order = o
                break

        if not order:
            flash("Order not found or you don't have permission to view it!", "danger")
            return redirect(url_for('my_orders'))

        # Enhance order items with category information from grocery_data
        grocery_items = load_grocery_data()
        grocery_lookup = {item.get('name', '').lower(): item for item in grocery_items}

        # Add categories to order items if missing
        if order.get('items'):
            for item in order['items']:
                if not item.get('category') or item.get('category') == 'Uncategorized':
                    item_name = item.get('name', '').lower()
                    if item_name in grocery_lookup:
                        item['category'] = grocery_lookup[item_name].get('category', 'Uncategorized')

        # Calculate order progress percentage
        status_progress = {
            'pending': 20,
            'confirmed': 40,
            'processing': 60,
            'shipped': 80,
            'delivered': 100,
            'cancelled': 0
        }

        order['progress_percentage'] = status_progress.get(order.get('status', 'pending'), 20)

        return render_template('customer_order_tracking.html',
                             title=f'Track Order #{order_id[:8]}',
                             order=order)
    except Exception as e:
        app.logger.error(f"Error in track_order: {str(e)}")
        flash(f"Error loading order details: {str(e)}", "danger")
        return redirect(url_for('my_orders'))


@app.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """Change user password"""
    form = ChangePasswordForm()

    if form.validate_on_submit():
        if current_user.check_password(form.current_password.data):
            current_user.set_password(form.new_password.data)
            db.session.commit()
            flash('Your password has been changed successfully!', 'success')
            return redirect(url_for('profile'))
        else:
            flash('Current password is incorrect.', 'error')

    return render_template('auth/change_password.html', title='Change Password', form=form)


# ===== ADMIN USER MANAGEMENT ROUTES =====

def admin_required(f):
    """Decorator to require admin access"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin():
            flash('Admin access required.', 'error')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function


@app.route('/admin/users')
@login_required
@admin_required
def admin_users():
    """Admin user management page"""
    users = User.query.all()
    return render_template('admin/users.html', title='User Management', users=users)


@app.route('/admin/users/create', methods=['GET', 'POST'])
@login_required
@admin_required
def admin_create_user():
    """Create new user (admin only)"""
    form = AdminUserForm()

    if form.validate_on_submit():
        user = User(
            username=form.username.data,
            email=form.email.data,
            password='temppassword123',  # Temporary password
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            role=form.role.data,
            is_active=form.is_active.data,
            is_verified=form.is_verified.data
        )

        db.session.add(user)
        db.session.commit()

        flash(f'User {user.username} created successfully! Temporary password: temppassword123', 'success')
        return redirect(url_for('admin_users'))

    return render_template('admin/create_user.html', title='Create User', form=form)


@app.route('/admin/users/<user_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def admin_edit_user(user_id):
    """Edit user (admin only)"""
    user = User.query.get_or_404(user_id)
    form = AdminUserForm()

    if form.validate_on_submit():
        # Check if username/email changed and if they're unique
        if form.username.data != user.username:
            existing_user = User.query.filter_by(username=form.username.data).first()
            if existing_user:
                flash('Username already exists.', 'error')
                return render_template('admin/edit_user.html', title='Edit User', form=form, user=user)

        if form.email.data != user.email:
            existing_user = User.query.filter_by(email=form.email.data).first()
            if existing_user:
                flash('Email already exists.', 'error')
                return render_template('admin/edit_user.html', title='Edit User', form=form, user=user)

        user.username = form.username.data
        user.email = form.email.data
        user.first_name = form.first_name.data
        user.last_name = form.last_name.data
        user.role = form.role.data
        user.is_active = form.is_active.data
        user.is_verified = form.is_verified.data

        db.session.commit()
        flash(f'User {user.username} updated successfully!', 'success')
        return redirect(url_for('admin_users'))

    elif request.method == 'GET':
        form.username.data = user.username
        form.email.data = user.email
        form.first_name.data = user.first_name
        form.last_name.data = user.last_name
        form.role.data = user.role
        form.is_active.data = user.is_active
        form.is_verified.data = user.is_verified

    return render_template('admin/edit_user.html', title='Edit User', form=form, user=user)


@app.route('/admin/users/<user_id>/delete', methods=['POST'])
@login_required
@admin_required
def admin_delete_user(user_id):
    """Delete user (admin only)"""
    user = User.query.get_or_404(user_id)

    if user.id == current_user.id:
        flash('You cannot delete your own account.', 'error')
        return redirect(url_for('admin_users'))

    username = user.username
    db.session.delete(user)
    db.session.commit()

    flash(f'User {username} deleted successfully.', 'success')
    return redirect(url_for('admin_users'))


@app.route('/admin/users/<user_id>/toggle-status', methods=['POST'])
@login_required
@admin_required
def admin_toggle_user_status(user_id):
    """Toggle user active status (admin only)"""
    user = User.query.get_or_404(user_id)

    if user.id == current_user.id:
        flash('You cannot deactivate your own account.', 'error')
        return redirect(url_for('admin_users'))

    user.is_active = not user.is_active
    db.session.commit()

    status = 'activated' if user.is_active else 'deactivated'
    flash(f'User {user.username} {status} successfully.', 'success')
    return redirect(url_for('admin_users'))


# ===== PRODUCT MANAGEMENT ROUTES =====

@app.route('/admin/categories')
@login_required
@admin_required
def admin_categories():
    """Admin category management page"""
    categories = Category.query.order_by(Category.sort_order, Category.name).all()
    return render_template('admin/categories.html', title='Category Management', categories=categories)


@app.route('/admin/categories/create', methods=['GET', 'POST'])
@login_required
@admin_required
def admin_create_category():
    """Create new category"""
    form = CategoryForm()

    if form.validate_on_submit():
        category = Category(
            name=form.name.data,
            description=form.description.data,
            parent_id=form.parent_id.data if form.parent_id.data else None,
            icon=form.icon.data,
            color=form.color.data,
            is_active=form.is_active.data,
            sort_order=form.sort_order.data,
            meta_title=form.meta_title.data,
            meta_description=form.meta_description.data
        )

        db.session.add(category)
        db.session.commit()

        flash(f'Category "{category.name}" created successfully!', 'success')
        return redirect(url_for('admin_categories'))

    return render_template('admin/create_category.html', title='Create Category', form=form)


@app.route('/admin/categories/<category_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def admin_edit_category(category_id):
    """Edit category"""
    category = Category.query.get_or_404(category_id)
    form = CategoryForm(category=category)

    if form.validate_on_submit():
        category.name = form.name.data
        category.description = form.description.data
        category.parent_id = form.parent_id.data if form.parent_id.data else None
        category.icon = form.icon.data
        category.color = form.color.data
        category.is_active = form.is_active.data
        category.sort_order = form.sort_order.data
        category.meta_title = form.meta_title.data
        category.meta_description = form.meta_description.data
        category.slug = category.generate_slug(form.name.data)

        db.session.commit()
        flash(f'Category "{category.name}" updated successfully!', 'success')
        return redirect(url_for('admin_categories'))

    elif request.method == 'GET':
        form.name.data = category.name
        form.description.data = category.description
        form.parent_id.data = category.parent_id
        form.icon.data = category.icon
        form.color.data = category.color
        form.is_active.data = category.is_active
        form.sort_order.data = category.sort_order
        form.meta_title.data = category.meta_title
        form.meta_description.data = category.meta_description

    return render_template('admin/edit_category.html', title='Edit Category', form=form, category=category)


@app.route('/admin/categories/<category_id>/delete', methods=['POST'])
@login_required
@admin_required
def admin_delete_category(category_id):
    """Delete category"""
    category = Category.query.get_or_404(category_id)

    # Check if category has products
    if category.products:
        flash(f'Cannot delete category "{category.name}" because it contains products.', 'error')
        return redirect(url_for('admin_categories'))

    # Check if category has subcategories
    if category.subcategories:
        flash(f'Cannot delete category "{category.name}" because it has subcategories.', 'error')
        return redirect(url_for('admin_categories'))

    name = category.name
    db.session.delete(category)
    db.session.commit()

    flash(f'Category "{name}" deleted successfully.', 'success')
    return redirect(url_for('admin_categories'))


@app.route('/admin/dashboard/new')
@login_required
@admin_required
def admin_dashboard_new():
    """New admin dashboard with product management overview (Flask-Login)"""
    # Get product statistics
    total_products = Product.query.filter_by(is_active=True).count()
    total_categories = Category.query.filter_by(is_active=True).count()

    # Stock statistics
    out_of_stock = Product.query.filter(
        Product.track_inventory == True,
        Product.stock_quantity == 0,
        Product.is_active == True
    ).count()

    low_stock = Product.query.filter(
        Product.track_inventory == True,
        Product.stock_quantity <= Product.low_stock_threshold,
        Product.stock_quantity > 0,
        Product.is_active == True
    ).count()

    # Featured products
    featured_products = Product.query.filter_by(is_featured=True, is_active=True).count()

    # Recent products
    recent_products = Product.query.filter_by(is_active=True).order_by(Product.created_at.desc()).limit(5).all()

    # Recent inventory changes
    recent_inventory = InventoryLog.query.order_by(InventoryLog.created_at.desc()).limit(10).all()

    # Top categories by product count
    from sqlalchemy import func
    top_categories = db.session.query(
        Category.name,
        func.count(Product.id).label('product_count')
    ).join(Product).filter(
        Category.is_active == True,
        Product.is_active == True
    ).group_by(Category.id, Category.name).order_by(
        func.count(Product.id).desc()
    ).limit(5).all()

    # Calculate total inventory value
    total_value = db.session.query(
        func.sum(Product.stock_quantity * Product.price)
    ).filter(Product.is_active == True).scalar() or 0

    stats = {
        'total_products': total_products,
        'total_categories': total_categories,
        'out_of_stock': out_of_stock,
        'low_stock': low_stock,
        'featured_products': featured_products,
        'total_inventory_value': float(total_value)
    }

    return render_template('admin/dashboard.html', title='Product Management Dashboard',
                         stats=stats, recent_products=recent_products,
                         recent_inventory=recent_inventory, top_categories=top_categories)


@app.route('/admin/products')
@login_required
@admin_required
def admin_products():
    """Admin product management page"""
    form = ProductSearchForm()

    # Build query
    query = Product.query

    # Apply filters if form is submitted
    if request.args.get('search'):
        search_term = request.args.get('search')
        query = query.filter(Product.name.contains(search_term))
        form.search.data = search_term

    if request.args.get('category_id'):
        category_id = request.args.get('category_id')
        query = query.filter(Product.category_id == category_id)
        form.category_id.data = category_id

    if request.args.get('in_stock_only') == 'y':
        query = query.filter(Product.stock_quantity > 0)
        form.in_stock_only.data = True

    if request.args.get('featured_only') == 'y':
        query = query.filter(Product.is_featured == True)
        form.featured_only.data = True

    if request.args.get('active_only', 'y') == 'y':
        query = query.filter(Product.is_active == True)
        form.active_only.data = True

    # Apply sorting
    sort_by = request.args.get('sort_by', 'name_asc')
    form.sort_by.data = sort_by

    if sort_by == 'name_desc':
        query = query.order_by(Product.name.desc())
    elif sort_by == 'price_asc':
        query = query.order_by(Product.price.asc())
    elif sort_by == 'price_desc':
        query = query.order_by(Product.price.desc())
    elif sort_by == 'created_desc':
        query = query.order_by(Product.created_at.desc())
    elif sort_by == 'created_asc':
        query = query.order_by(Product.created_at.asc())
    elif sort_by == 'stock_asc':
        query = query.order_by(Product.stock_quantity.asc())
    elif sort_by == 'stock_desc':
        query = query.order_by(Product.stock_quantity.desc())
    else:  # name_asc
        query = query.order_by(Product.name.asc())

    # Pagination
    page = request.args.get('page', 1, type=int)
    products = query.paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('admin/products.html', title='Product Management',
                         products=products, form=form)


@app.route('/admin/products/create', methods=['GET', 'POST'])
@login_required
@admin_required
def admin_create_product():
    """Create new product"""
    form = ProductForm()

    if form.validate_on_submit():
        product = Product(
            name=form.name.data,
            category_id=form.category_id.data,
            price=form.price.data,
            sku=form.sku.data,
            barcode=form.barcode.data,
            description=form.description.data,
            short_description=form.short_description.data,
            cost_price=form.cost_price.data,
            compare_price=form.compare_price.data,
            stock_quantity=form.stock_quantity.data,
            low_stock_threshold=form.low_stock_threshold.data,
            track_inventory=form.track_inventory.data,
            allow_backorder=form.allow_backorder.data,
            weight=form.weight.data,
            dimensions=form.dimensions.data,
            unit=form.unit.data,
            unit_quantity=form.unit_quantity.data,
            is_active=form.is_active.data,
            is_featured=form.is_featured.data,
            requires_shipping=form.requires_shipping.data,
            ingredients=form.ingredients.data,
            nutritional_info=form.nutritional_info.data,
            tags=form.tags.data,
            meta_title=form.meta_title.data,
            meta_description=form.meta_description.data
        )

        db.session.add(product)
        db.session.commit()

        # Log initial inventory
        if form.stock_quantity.data > 0:
            product.update_stock(0, "Initial stock", current_user.id)

        flash(f'Product "{product.name}" created successfully!', 'success')
        return redirect(url_for('admin_products'))

    return render_template('admin/create_product.html', title='Create Product', form=form)


@app.route('/admin/products/<product_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def admin_edit_product(product_id):
    """Edit product"""
    product = Product.query.get_or_404(product_id)
    form = ProductForm(product=product)

    if form.validate_on_submit():
        # Track stock changes
        old_stock = product.stock_quantity
        new_stock = form.stock_quantity.data

        # Update product fields
        product.name = form.name.data
        product.category_id = form.category_id.data
        product.price = form.price.data
        product.sku = form.sku.data
        product.barcode = form.barcode.data
        product.description = form.description.data
        product.short_description = form.short_description.data
        product.cost_price = form.cost_price.data
        product.compare_price = form.compare_price.data
        product.stock_quantity = new_stock
        product.low_stock_threshold = form.low_stock_threshold.data
        product.track_inventory = form.track_inventory.data
        product.allow_backorder = form.allow_backorder.data
        product.weight = form.weight.data
        product.dimensions = form.dimensions.data
        product.unit = form.unit.data
        product.unit_quantity = form.unit_quantity.data
        product.is_active = form.is_active.data
        product.is_featured = form.is_featured.data
        product.requires_shipping = form.requires_shipping.data
        product.ingredients = form.ingredients.data
        product.nutritional_info = form.nutritional_info.data
        product.tags = form.tags.data
        product.meta_title = form.meta_title.data
        product.meta_description = form.meta_description.data
        product.slug = product.generate_slug(form.name.data)

        db.session.commit()

        # Log stock change if quantity changed
        if old_stock != new_stock:
            product.update_stock(new_stock - old_stock, "Manual adjustment via edit", current_user.id)

        flash(f'Product "{product.name}" updated successfully!', 'success')
        return redirect(url_for('admin_products'))

    elif request.method == 'GET':
        # Populate form with existing data
        form.name.data = product.name
        form.category_id.data = product.category_id
        form.price.data = product.price
        form.sku.data = product.sku
        form.barcode.data = product.barcode
        form.description.data = product.description
        form.short_description.data = product.short_description
        form.cost_price.data = product.cost_price
        form.compare_price.data = product.compare_price
        form.stock_quantity.data = product.stock_quantity
        form.low_stock_threshold.data = product.low_stock_threshold
        form.track_inventory.data = product.track_inventory
        form.allow_backorder.data = product.allow_backorder
        form.weight.data = product.weight
        form.dimensions.data = product.dimensions
        form.unit.data = product.unit
        form.unit_quantity.data = product.unit_quantity
        form.is_active.data = product.is_active
        form.is_featured.data = product.is_featured
        form.requires_shipping.data = product.requires_shipping
        form.ingredients.data = product.ingredients
        form.nutritional_info.data = product.nutritional_info
        form.tags.data = product.tags
        form.meta_title.data = product.meta_title
        form.meta_description.data = product.meta_description

    return render_template('admin/edit_product.html', title='Edit Product', form=form, product=product)


@app.route('/admin/products/<product_id>/delete', methods=['POST'])
@login_required
@admin_required
def admin_delete_product(product_id):
    """Delete product"""
    product = Product.query.get_or_404(product_id)

    name = product.name
    db.session.delete(product)
    db.session.commit()

    flash(f'Product "{name}" deleted successfully.', 'success')
    return redirect(url_for('admin_products'))


@app.route('/admin/products/<product_id>/toggle-status', methods=['POST'])
@login_required
@admin_required
def admin_toggle_product_status(product_id):
    """Toggle product active status"""
    product = Product.query.get_or_404(product_id)

    product.is_active = not product.is_active
    db.session.commit()

    status = 'activated' if product.is_active else 'deactivated'
    flash(f'Product "{product.name}" {status} successfully.', 'success')
    return redirect(url_for('admin_products'))


@app.route('/admin/products/<product_id>/images')
@login_required
@admin_required
def admin_product_images(product_id):
    """Manage product images"""
    product = Product.query.get_or_404(product_id)
    images = ProductImage.query.filter_by(product_id=product_id).order_by(ProductImage.sort_order).all()
    form = ProductImageForm()

    return render_template('admin/product_images.html', title='Product Images',
                         product=product, images=images, form=form)


@app.route('/admin/products/<product_id>/images/upload', methods=['POST'])
@login_required
@admin_required
def admin_upload_product_image(product_id):
    """Upload product image"""
    product = Product.query.get_or_404(product_id)
    form = ProductImageForm()

    if form.validate_on_submit():
        # Save image
        product_image = save_product_image(form.image.data, product_id)

        if product_image:
            # Set alt text
            if form.alt_text.data:
                product_image.alt_text = form.alt_text.data

            # Set as main image if requested or if it's the first image
            if form.is_main.data or not ProductImage.query.filter_by(product_id=product_id, is_main=True).first():
                # Remove main flag from other images
                ProductImage.query.filter_by(product_id=product_id, is_main=True).update({'is_main': False})
                product_image.is_main = True

            db.session.commit()
            flash('Image uploaded successfully!', 'success')
        else:
            flash('Error uploading image. Please try again.', 'error')
    else:
        for field, errors in form.errors.items():
            for error in errors:
                flash(f'{field}: {error}', 'error')

    return redirect(url_for('admin_product_images', product_id=product_id))


@app.route('/admin/products/<product_id>/images/<image_id>/delete', methods=['POST'])
@login_required
@admin_required
def admin_delete_product_image(product_id, image_id):
    """Delete product image"""
    product = Product.query.get_or_404(product_id)
    image = ProductImage.query.get_or_404(image_id)

    if image.product_id != product_id:
        flash('Invalid image for this product.', 'error')
        return redirect(url_for('admin_product_images', product_id=product_id))

    # Delete physical file
    try:
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], 'products', product_id, image.filename)
        if os.path.exists(file_path):
            os.remove(file_path)
    except Exception as e:
        app.logger.error(f"Error deleting image file: {str(e)}")

    # Delete database record
    db.session.delete(image)
    db.session.commit()

    flash('Image deleted successfully.', 'success')
    return redirect(url_for('admin_product_images', product_id=product_id))


@app.route('/admin/products/<product_id>/images/<image_id>/set-main', methods=['POST'])
@login_required
@admin_required
def admin_set_main_image(product_id, image_id):
    """Set image as main product image"""
    product = Product.query.get_or_404(product_id)
    image = ProductImage.query.get_or_404(image_id)

    if image.product_id != product_id:
        flash('Invalid image for this product.', 'error')
        return redirect(url_for('admin_product_images', product_id=product_id))

    # Remove main flag from all images
    ProductImage.query.filter_by(product_id=product_id).update({'is_main': False})

    # Set this image as main
    image.is_main = True
    db.session.commit()

    flash('Main image updated successfully.', 'success')
    return redirect(url_for('admin_product_images', product_id=product_id))


@app.route('/admin/products/<product_id>/inventory', methods=['GET', 'POST'])
@login_required
@admin_required
def admin_product_inventory(product_id):
    """Manage product inventory"""
    product = Product.query.get_or_404(product_id)
    form = InventoryAdjustmentForm()
    form.product_id.data = product_id

    if form.validate_on_submit():
        adjustment_type = form.adjustment_type.data
        quantity = form.quantity.data
        reason = form.reason.data
        notes = form.notes.data

        if adjustment_type == 'add':
            quantity_change = quantity
        elif adjustment_type == 'remove':
            quantity_change = -quantity
        else:  # set
            quantity_change = quantity - product.stock_quantity

        # Update stock
        product.update_stock(quantity_change, reason, current_user.id)

        # Add notes to the log if provided
        if notes:
            latest_log = InventoryLog.query.filter_by(product_id=product_id).order_by(InventoryLog.created_at.desc()).first()
            if latest_log:
                latest_log.notes = notes
                db.session.commit()

        flash(f'Inventory updated successfully. New stock: {product.stock_quantity}', 'success')
        return redirect(url_for('admin_product_inventory', product_id=product_id))

    # Get inventory logs
    logs = InventoryLog.query.filter_by(product_id=product_id).order_by(InventoryLog.created_at.desc()).limit(20).all()

    return render_template('admin/product_inventory.html', title='Product Inventory',
                         product=product, form=form, logs=logs)


@app.route('/admin/inventory/low-stock')
@login_required
@admin_required
def admin_low_stock():
    """View products with low stock"""
    low_stock_products = Product.query.filter(
        Product.track_inventory == True,
        Product.stock_quantity <= Product.low_stock_threshold,
        Product.is_active == True
    ).order_by(Product.stock_quantity.asc()).all()

    return render_template('admin/low_stock.html', title='Low Stock Alert', products=low_stock_products)


@app.route('/admin/inventory/reports')
@login_required
@admin_required
def admin_inventory_reports():
    """Inventory reports and analytics"""
    # Get inventory statistics
    total_products = Product.query.filter_by(is_active=True).count()
    out_of_stock = Product.query.filter(
        Product.track_inventory == True,
        Product.stock_quantity == 0,
        Product.is_active == True
    ).count()
    low_stock = Product.query.filter(
        Product.track_inventory == True,
        Product.stock_quantity <= Product.low_stock_threshold,
        Product.stock_quantity > 0,
        Product.is_active == True
    ).count()

    # Get recent inventory movements
    recent_logs = InventoryLog.query.order_by(InventoryLog.created_at.desc()).limit(50).all()

    # Get top products by stock value
    from sqlalchemy import func
    top_value_products = db.session.query(
        Product,
        (Product.stock_quantity * Product.price).label('stock_value')
    ).filter(Product.is_active == True).order_by(
        (Product.stock_quantity * Product.price).desc()
    ).limit(10).all()

    stats = {
        'total_products': total_products,
        'out_of_stock': out_of_stock,
        'low_stock': low_stock,
        'in_stock': total_products - out_of_stock
    }

    return render_template('admin/inventory_reports.html', title='Inventory Reports',
                         stats=stats, recent_logs=recent_logs, top_value_products=top_value_products)


# ===== CUSTOMER PRODUCT ROUTES =====

@app.route('/products')
def products():
    """Customer product listing with search and filters"""
    form = ProductSearchForm()

    # Build query for active products only
    query = Product.query.filter_by(is_active=True)

    # Apply search filters
    if request.args.get('search'):
        search_term = request.args.get('search')
        query = query.filter(
            db.or_(
                Product.name.contains(search_term),
                Product.description.contains(search_term),
                Product.tags.contains(search_term)
            )
        )
        form.search.data = search_term

    if request.args.get('category_id'):
        category_id = request.args.get('category_id')
        query = query.filter(Product.category_id == category_id)
        form.category_id.data = category_id

    if request.args.get('min_price'):
        min_price = float(request.args.get('min_price'))
        query = query.filter(Product.price >= min_price)
        form.min_price.data = min_price

    if request.args.get('max_price'):
        max_price = float(request.args.get('max_price'))
        query = query.filter(Product.price <= max_price)
        form.max_price.data = max_price

    if request.args.get('in_stock_only') == 'y':
        query = query.filter(Product.stock_quantity > 0)
        form.in_stock_only.data = True

    if request.args.get('featured_only') == 'y':
        query = query.filter(Product.is_featured == True)
        form.featured_only.data = True

    # Apply sorting
    sort_by = request.args.get('sort_by', 'name_asc')
    form.sort_by.data = sort_by

    if sort_by == 'name_desc':
        query = query.order_by(Product.name.desc())
    elif sort_by == 'price_asc':
        query = query.order_by(Product.price.asc())
    elif sort_by == 'price_desc':
        query = query.order_by(Product.price.desc())
    elif sort_by == 'created_desc':
        query = query.order_by(Product.created_at.desc())
    elif sort_by == 'created_asc':
        query = query.order_by(Product.created_at.asc())
    else:  # name_asc
        query = query.order_by(Product.name.asc())

    # Pagination
    page = request.args.get('page', 1, type=int)
    products = query.paginate(
        page=page, per_page=12, error_out=False
    )

    # Get categories for filter
    categories = Category.query.filter_by(is_active=True).order_by(Category.name).all()

    return render_template('products.html', title='Products',
                         products=products, form=form, categories=categories)


@app.route('/products/<slug>')
def product_detail(slug):
    """Product detail page"""
    product = Product.query.filter_by(slug=slug, is_active=True).first_or_404()

    # Get related products from same category
    related_products = Product.query.filter(
        Product.category_id == product.category_id,
        Product.id != product.id,
        Product.is_active == True
    ).limit(4).all()

    return render_template('product_detail.html', title=product.name,
                         product=product, related_products=related_products)


@app.route('/categories')
def categories():
    """Category listing page"""
    categories = Category.query.filter_by(is_active=True, parent_id=None).order_by(Category.sort_order, Category.name).all()
    return render_template('categories.html', title='Categories', categories=categories)


@app.route('/categories/<slug>')
def category_products(slug):
    """Products in a specific category"""
    category = Category.query.filter_by(slug=slug, is_active=True).first_or_404()

    # Get products in this category and subcategories
    category_ids = [category.id]
    for subcategory in category.subcategories:
        if subcategory.is_active:
            category_ids.append(subcategory.id)

    query = Product.query.filter(
        Product.category_id.in_(category_ids),
        Product.is_active == True
    )

    # Apply sorting
    sort_by = request.args.get('sort_by', 'name_asc')
    if sort_by == 'name_desc':
        query = query.order_by(Product.name.desc())
    elif sort_by == 'price_asc':
        query = query.order_by(Product.price.asc())
    elif sort_by == 'price_desc':
        query = query.order_by(Product.price.desc())
    else:  # name_asc
        query = query.order_by(Product.name.asc())

    # Pagination
    page = request.args.get('page', 1, type=int)
    products = query.paginate(
        page=page, per_page=12, error_out=False
    )

    return render_template('category_products.html', title=category.name,
                         category=category, products=products)


@app.route('/search')
def search():
    """Search products"""
    query_string = request.args.get('q', '')

    if not query_string:
        return redirect(url_for('products'))

    # Search in product name, description, and tags
    products = Product.query.filter(
        db.or_(
            Product.name.contains(query_string),
            Product.description.contains(query_string),
            Product.tags.contains(query_string)
        ),
        Product.is_active == True
    ).order_by(Product.name.asc()).all()

    return render_template('search_results.html', title=f'Search: {query_string}',
                         products=products, query=query_string)


def init_database():
    """Initialize database with sample data"""
    with app.app_context():
        # Create all tables
        db.create_all()

        # Create default admin user if it doesn't exist
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                is_admin=True,
                is_active=True
            )
            admin_user.set_password('admin123')  # Change this in production!
            db.session.add(admin_user)
            db.session.commit()
            print("Default admin user created (username: admin, password: admin123)")

        # Check if we need to create sample data
        if Category.query.count() == 0:
            # Create sample categories
            fruits_cat = Category(name='Fruits', description='Fresh fruits and produce', is_active=True, sort_order=1)
            dairy_cat = Category(name='Dairy', description='Milk, cheese, and dairy products', is_active=True, sort_order=2)
            vegetables_cat = Category(name='Vegetables', description='Fresh vegetables', is_active=True, sort_order=3)

            db.session.add_all([fruits_cat, dairy_cat, vegetables_cat])
            db.session.commit()

            # Create sample products
            products = [
                Product(
                    name='Apples',
                    price=1.99,
                    stock_quantity=50,
                    category_id=fruits_cat.id,
                    description='Fresh red apples',
                    short_description='Fresh red apples',
                    is_active=True,
                    is_featured=True
                ),
                Product(
                    name='Bananas',
                    price=0.99,
                    stock_quantity=40,
                    category_id=fruits_cat.id,
                    description='Ripe yellow bananas',
                    short_description='Ripe yellow bananas',
                    is_active=True,
                    is_featured=True
                ),
                Product(
                    name='Milk',
                    price=2.49,
                    stock_quantity=20,
                    category_id=dairy_cat.id,
                    description='Fresh whole milk',
                    short_description='Fresh whole milk',
                    is_active=True,
                    is_featured=True
                ),
                Product(
                    name='Carrots',
                    price=1.29,
                    stock_quantity=30,
                    category_id=vegetables_cat.id,
                    description='Fresh organic carrots',
                    short_description='Fresh organic carrots',
                    is_active=True,
                    is_featured=True
                )
            ]

            db.session.add_all(products)
            db.session.commit()

            print("Database initialized with sample data")


if __name__ == '__main__':
    print("Starting Flask application...")

    # Initialize database
    print("Initializing database...")
    init_database()
    print("Database initialized successfully!")

    print("Starting Flask server on http://127.0.0.1:5000")
    app.run(debug=True, host='127.0.0.1', port=5000)
