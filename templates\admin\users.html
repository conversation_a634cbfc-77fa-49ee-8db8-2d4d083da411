{% extends "base.html" %}
{% block title %}User Management - Fresh Grocery Admin{% endblock %}

{% block content %}
<div class="container py-5">
    <!-- Admin Header -->
    <div class="admin-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="admin-title">
                    <i class="fas fa-users"></i> User Management
                </h1>
                <p class="admin-subtitle">Manage user accounts, roles, and permissions</p>
            </div>
            <div class="col-auto">
                <a href="{{ url_for('admin_create_user') }}" class="btn admin-btn-primary">
                    <i class="fas fa-user-plus"></i> Create New User
                </a>
            </div>
        </div>
    </div>
    
    <!-- Users Table -->
    <div class="admin-card">
        <div class="admin-card-header">
            <h5 class="admin-card-title">
                <i class="fas fa-list"></i> All Users ({{ users|length }})
            </h5>
        </div>
        <div class="admin-card-body">
            {% if users %}
                <div class="table-responsive">
                    <table class="table admin-table">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Contact</th>
                                <th>Role</th>
                                <th>Status</th>
                                <th>Joined</th>
                                <th>Last Login</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users %}
                            <tr>
                                <td>
                                    <div class="user-info">
                                        <div class="user-avatar">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="user-details">
                                            <div class="user-name">{{ user.get_full_name() }}</div>
                                            <div class="user-username">@{{ user.username }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="contact-info">
                                        <div class="contact-email">{{ user.email }}</div>
                                        <div class="contact-phone">{{ user.phone if user.phone else 'No phone' }}</div>
                                    </div>
                                </td>
                                <td>
                                    <span class="role-badge role-{{ user.role }}">
                                        {{ user.role.title() }}
                                    </span>
                                </td>
                                <td>
                                    <div class="status-badges">
                                        {% if user.is_active %}
                                            <span class="status-badge status-active">Active</span>
                                        {% else %}
                                            <span class="status-badge status-inactive">Inactive</span>
                                        {% endif %}
                                        
                                        {% if user.is_verified %}
                                            <span class="status-badge status-verified">Verified</span>
                                        {% else %}
                                            <span class="status-badge status-unverified">Unverified</span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <div class="date-info">
                                        {{ user.created_at.strftime('%b %d, %Y') if user.created_at else 'Unknown' }}
                                    </div>
                                </td>
                                <td>
                                    <div class="date-info">
                                        {{ user.last_login.strftime('%b %d, %Y') if user.last_login else 'Never' }}
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="{{ url_for('admin_edit_user', user_id=user.id) }}" 
                                           class="btn btn-sm admin-btn-secondary" title="Edit User">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        
                                        {% if user.id != current_user.id %}
                                            <form method="POST" action="{{ url_for('admin_toggle_user_status', user_id=user.id) }}" 
                                                  style="display: inline;">
                                                <button type="submit" 
                                                        class="btn btn-sm {{ 'admin-btn-warning' if user.is_active else 'admin-btn-success' }}" 
                                                        title="{{ 'Deactivate' if user.is_active else 'Activate' }} User">
                                                    <i class="fas {{ 'fa-user-slash' if user.is_active else 'fa-user-check' }}"></i>
                                                </button>
                                            </form>
                                            
                                            <form method="POST" action="{{ url_for('admin_delete_user', user_id=user.id) }}" 
                                                  style="display: inline;" 
                                                  onsubmit="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                                                <button type="submit" class="btn btn-sm admin-btn-danger" title="Delete User">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        {% else %}
                                            <span class="text-muted small">Current User</span>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h5 class="empty-title">No Users Found</h5>
                    <p class="empty-text">There are no users in the system yet.</p>
                    <a href="{{ url_for('admin_create_user') }}" class="btn admin-btn-primary">
                        <i class="fas fa-user-plus"></i> Create First User
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
/* Admin Styles */
.admin-header {
    background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
    border-radius: 15px;
    padding: 2rem;
    border: 2px solid #e8f5e8;
}

.admin-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1B5E20;
    margin-bottom: 0.5rem;
}

.admin-subtitle {
    color: #2E7D32;
    font-size: 1.1rem;
    margin: 0;
}

.admin-btn-primary {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    border: none;
    border-radius: 10px;
    padding: 10px 20px;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(46, 125, 50, 0.3);
}

.admin-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(46, 125, 50, 0.4);
    color: white;
}

.admin-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(46, 125, 50, 0.1);
    border: 2px solid #e8f5e8;
    overflow: hidden;
}

.admin-card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1.5rem;
    border-bottom: 1px solid #e8f5e8;
}

.admin-card-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1B5E20;
    margin: 0;
}

.admin-card-body {
    padding: 0;
}

.admin-table {
    margin: 0;
}

.admin-table th {
    background: #f8f9fa;
    color: #2E7D32;
    font-weight: 600;
    border: none;
    padding: 1rem;
}

.admin-table td {
    padding: 1rem;
    border-top: 1px solid #e9ecef;
    vertical-align: middle;
}

.user-info {
    display: flex;
    align-items: center;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    color: white;
}

.user-name {
    font-weight: 600;
    color: #333;
}

.user-username {
    font-size: 0.9rem;
    color: #6c757d;
}

.contact-email {
    font-size: 0.9rem;
    color: #333;
}

.contact-phone {
    font-size: 0.8rem;
    color: #6c757d;
}

.role-badge {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.role-admin {
    background: #d4edda;
    color: #155724;
}

.role-staff {
    background: #d1ecf1;
    color: #0c5460;
}

.role-customer {
    background: #fff3cd;
    color: #856404;
}

.status-badges {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.status-badge {
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    text-align: center;
}

.status-active {
    background: #d4edda;
    color: #155724;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
}

.status-verified {
    background: #d1ecf1;
    color: #0c5460;
}

.status-unverified {
    background: #fff3cd;
    color: #856404;
}

.date-info {
    font-size: 0.9rem;
    color: #6c757d;
}

.action-buttons {
    display: flex;
    gap: 0.25rem;
}

.admin-btn-secondary {
    background: transparent;
    border: 1px solid #4CAF50;
    color: #2E7D32;
}

.admin-btn-secondary:hover {
    background: #4CAF50;
    color: white;
}

.admin-btn-warning {
    background: transparent;
    border: 1px solid #ffc107;
    color: #856404;
}

.admin-btn-warning:hover {
    background: #ffc107;
    color: #212529;
}

.admin-btn-success {
    background: transparent;
    border: 1px solid #28a745;
    color: #155724;
}

.admin-btn-success:hover {
    background: #28a745;
    color: white;
}

.admin-btn-danger {
    background: transparent;
    border: 1px solid #dc3545;
    color: #721c24;
}

.admin-btn-danger:hover {
    background: #dc3545;
    color: white;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
}

.empty-icon {
    font-size: 4rem;
    color: #e9ecef;
    margin-bottom: 1rem;
}

.empty-title {
    color: #6c757d;
    margin-bottom: 1rem;
}

.empty-text {
    color: #6c757d;
    margin-bottom: 2rem;
}
</style>
{% endblock %}
