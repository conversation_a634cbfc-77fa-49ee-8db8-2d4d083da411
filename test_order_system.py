#!/usr/bin/env python3

import os
import sys
import json

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from models import User

def test_order_system():
    """Test the order tracking system functionality"""
    
    with app.app_context():
        print("=== TESTING ORDER TRACKING SYSTEM ===\n")
        
        # Test 1: Check user exists
        user = User.query.filter_by(email='<EMAIL>').first()
        if user:
            print(f"✅ User found: {user.email} - {user.first_name} {user.last_name}")
        else:
            print("❌ User not found")
            return
        
        # Test 2: Load orders
        try:
            from app import load_orders
            orders = load_orders()
            print(f"✅ Orders loaded: {len(orders)} total orders")
        except Exception as e:
            print(f"❌ Error loading orders: {e}")
            return
        
        # Test 3: Filter orders for user
        customer_orders = []
        for order in orders:
            if order.get('email') == user.email:
                customer_orders.append(order)
        
        print(f"✅ Customer orders found: {len(customer_orders)}")
        
        # Test 4: Display order details
        if customer_orders:
            print("\n📦 CUSTOMER ORDERS:")
            for i, order in enumerate(customer_orders, 1):
                print(f"   {i}. Order #{order.get('order_id')}")
                print(f"      Date: {order.get('date')}")
                print(f"      Status: {order.get('status', 'No status')}")
                print(f"      Total: ${order.get('total')}")
                print(f"      Items: {len(order.get('items', []))} items")
                if order.get('items'):
                    for item in order.get('items', []):
                        print(f"         - {item.get('name')} x{item.get('quantity')} @ ${item.get('price')}")
                print()
        
        # Test 5: Test order statistics calculation
        order_stats = {
            'total_orders': len(customer_orders),
            'pending_orders': len([o for o in customer_orders if o.get('status') == 'pending']),
            'confirmed_orders': len([o for o in customer_orders if o.get('status') == 'confirmed']),
            'shipped_orders': len([o for o in customer_orders if o.get('status') == 'shipped']),
            'delivered_orders': len([o for o in customer_orders if o.get('status') == 'delivered']),
            'cancelled_orders': len([o for o in customer_orders if o.get('status') == 'cancelled']),
            'total_spent': sum(float(o.get('total', 0)) for o in customer_orders if o.get('status') != 'cancelled'),
        }
        
        print("📊 ORDER STATISTICS:")
        for key, value in order_stats.items():
            print(f"   {key}: {value}")
        
        # Test 6: Test template data structure
        print("\n🔧 TESTING TEMPLATE DATA:")
        for order in customer_orders[:1]:  # Test first order
            print(f"   Order ID: {order.get('order_id')}")
            print(f"   Has status: {'✅' if order.get('status') else '❌'}")
            print(f"   Has items: {'✅' if order.get('items') else '❌'}")
            print(f"   Has total: {'✅' if order.get('total') else '❌'}")
            print(f"   Has date: {'✅' if order.get('date') else '❌'}")
            
            if order.get('items'):
                item = order.get('items')[0]
                print(f"   First item structure:")
                print(f"      Name: {'✅' if item.get('name') else '❌'}")
                print(f"      Price: {'✅' if item.get('price') else '❌'}")
                print(f"      Quantity: {'✅' if item.get('quantity') else '❌'}")
                print(f"      Category: {'✅' if item.get('category') else '❌'}")
        
        print("\n🎉 ORDER TRACKING SYSTEM TEST COMPLETE!")
        print(f"   User: {user.email}")
        print(f"   Orders: {len(customer_orders)}")
        print(f"   System Status: {'✅ WORKING' if customer_orders else '❌ NO ORDERS'}")

if __name__ == "__main__":
    test_order_system()
