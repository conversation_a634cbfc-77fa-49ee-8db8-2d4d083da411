{% extends "base.html" %}
{% block title %}Track Order #{{ order.get('order_id', 'N/A')[:8] }} - Fresh Grocery{% endblock %}

{% block content %}
<div class="container py-5">
    <!-- Back Button -->
    <div class="mb-4">
        <a href="{{ url_for('my_orders') }}" class="btn btn-outline-fresh-secondary">
            <i class="fas fa-arrow-left"></i> Back to My Orders
        </a>
    </div>

    <!-- Order Header -->
    <div class="tracking-header mb-5">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2 class="tracking-title">
                    <i class="fas fa-search"></i> Track Order #{{ order.get('order_id', 'N/A')[:8] }}
                </h2>
                <p class="tracking-subtitle">Order placed on {{ order.get('date', 'Unknown')[:10] }}</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="order-total-display">
                    <span class="total-label">Order Total</span>
                    <span class="total-amount">${{ "%.2f"|format(order.get('total', 0)) }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Status Progress -->
    <div class="status-progress-section mb-5">
        <div class="progress-card">
            <div class="progress-header">
                <h4><i class="fas fa-truck"></i> Order Status</h4>
                <span class="current-status-badge 
                    {% if order.get('status') == 'pending' %}status-pending
                    {% elif order.get('status') == 'confirmed' %}status-confirmed
                    {% elif order.get('status') == 'processing' %}status-processing
                    {% elif order.get('status') == 'shipped' %}status-shipped
                    {% elif order.get('status') == 'delivered' %}status-delivered
                    {% elif order.get('status') == 'cancelled' %}status-cancelled
                    {% else %}status-unknown{% endif %}">
                    {{ order.get('status', 'Unknown').title() }}
                </span>
            </div>
            
            <!-- Progress Bar -->
            <div class="progress-container">
                <div class="progress-bar-custom">
                    <div class="progress-fill" style="width: {{ order.get('progress_percentage', 20) }}%"></div>
                </div>
                <div class="progress-steps">
                    <div class="step {% if order.get('progress_percentage', 0) >= 20 %}completed{% endif %}">
                        <div class="step-icon"><i class="fas fa-shopping-cart"></i></div>
                        <div class="step-label">Order Placed</div>
                    </div>
                    <div class="step {% if order.get('progress_percentage', 0) >= 40 %}completed{% endif %}">
                        <div class="step-icon"><i class="fas fa-check-circle"></i></div>
                        <div class="step-label">Confirmed</div>
                    </div>
                    <div class="step {% if order.get('progress_percentage', 0) >= 60 %}completed{% endif %}">
                        <div class="step-icon"><i class="fas fa-cog"></i></div>
                        <div class="step-label">Processing</div>
                    </div>
                    <div class="step {% if order.get('progress_percentage', 0) >= 80 %}completed{% endif %}">
                        <div class="step-icon"><i class="fas fa-truck"></i></div>
                        <div class="step-label">Shipped</div>
                    </div>
                    <div class="step {% if order.get('progress_percentage', 0) >= 100 %}completed{% endif %}">
                        <div class="step-icon"><i class="fas fa-home"></i></div>
                        <div class="step-label">Delivered</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Order Details -->
        <div class="col-lg-8 mb-4">
            <!-- Order Items -->
            <div class="section-card mb-4">
                <div class="section-header">
                    <h4><i class="fas fa-box"></i> Order Items</h4>
                </div>
                <div class="section-content">
                    {% if order.get('items') %}
                        <div class="items-list">
                            {% for item in order.get('items') %}
                            <div class="item-row">
                                <div class="item-info">
                                    <div class="item-name">{{ item.get('name', 'Unknown Item') }}</div>
                                    <div class="item-category">{{ item.get('category', 'Uncategorized') }}</div>
                                </div>
                                <div class="item-quantity">
                                    <span class="quantity-label">Qty:</span>
                                    <span class="quantity-value">{{ item.get('quantity', 1) }}</span>
                                </div>
                                <div class="item-price">
                                    <div class="unit-price">${{ "%.2f"|format(item.get('price', 0)) }} each</div>
                                    <div class="total-price">${{ "%.2f"|format(item.get('price', 0) * item.get('quantity', 1)) }}</div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        
                        <!-- Order Summary -->
                        <div class="order-summary">
                            <div class="summary-row">
                                <span>Subtotal ({{ order.get('items')|length }} items):</span>
                                <span>${{ "%.2f"|format(order.get('total', 0)) }}</span>
                            </div>
                            <div class="summary-row">
                                <span>Delivery Fee:</span>
                                <span>Free</span>
                            </div>
                            <div class="summary-row total-row">
                                <span>Total:</span>
                                <span>${{ "%.2f"|format(order.get('total', 0)) }}</span>
                            </div>
                        </div>
                    {% else %}
                        <div class="empty-items">
                            <p>No items found for this order.</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Status History -->
            {% if order.get('status_history') %}
            <div class="section-card">
                <div class="section-header">
                    <h4><i class="fas fa-history"></i> Order History</h4>
                </div>
                <div class="section-content">
                    <div class="timeline">
                        {% for change in order.get('status_history') %}
                        <div class="timeline-item">
                            <div class="timeline-marker"></div>
                            <div class="timeline-content">
                                <div class="timeline-header">
                                    <span class="status-change">
                                        {{ change.get('from_status', 'Unknown').title() }} → {{ change.get('to_status', 'Unknown').title() }}
                                    </span>
                                    <span class="timeline-date">{{ change.get('changed_at', 'Unknown') }}</span>
                                </div>
                                {% if change.get('notes') %}
                                <div class="timeline-notes">{{ change.get('notes') }}</div>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Customer Information -->
            <div class="section-card mb-4">
                <div class="section-header">
                    <h4><i class="fas fa-user"></i> Customer Information</h4>
                </div>
                <div class="section-content">
                    <div class="info-row">
                        <span class="info-label">Name:</span>
                        <span class="info-value">{{ order.get('name', 'N/A') }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Email:</span>
                        <span class="info-value">{{ order.get('email', 'N/A') }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Phone:</span>
                        <span class="info-value">{{ order.get('phone', 'N/A') }}</span>
                    </div>
                </div>
            </div>

            <!-- Delivery Information -->
            <div class="section-card mb-4">
                <div class="section-header">
                    <h4><i class="fas fa-map-marker-alt"></i> Delivery Address</h4>
                </div>
                <div class="section-content">
                    <div class="address-display">
                        {{ order.get('address', 'No address provided') }}
                    </div>
                </div>
            </div>

            <!-- Order Actions -->
            <div class="section-card">
                <div class="section-header">
                    <h4><i class="fas fa-cog"></i> Actions</h4>
                </div>
                <div class="section-content">
                    <div class="action-buttons">
                        {% if order.get('status') == 'pending' %}
                        <button class="btn btn-outline-danger w-100 mb-2" onclick="cancelOrder('{{ order.get('order_id') }}')">
                            <i class="fas fa-times"></i> Cancel Order
                        </button>
                        {% endif %}
                        <a href="{{ url_for('shop') }}" class="btn btn-fresh-primary w-100 mb-2">
                            <i class="fas fa-shopping-cart"></i> Order Again
                        </a>
                        <button class="btn btn-outline-fresh-secondary w-100" onclick="window.print()">
                            <i class="fas fa-print"></i> Print Order
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Tracking Page Styles */
.tracking-header {
    background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
    padding: 2rem;
    border-radius: 20px;
    border: 2px solid #e8f5e8;
}

.tracking-title {
    color: #2E7D32;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.tracking-subtitle {
    color: #666;
    margin-bottom: 0;
}

.order-total-display {
    text-align: right;
}

.total-label {
    display: block;
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.total-amount {
    display: block;
    color: #2E7D32;
    font-size: 2rem;
    font-weight: 700;
}

/* Progress Section */
.progress-card {
    background: white;
    border: 2px solid #e8f5e8;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(46, 125, 50, 0.1);
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.progress-header h4 {
    color: #2E7D32;
    margin-bottom: 0;
}

.current-status-badge {
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Progress Bar */
.progress-container {
    position: relative;
}

.progress-bar-custom {
    height: 8px;
    background: #e8f5e8;
    border-radius: 4px;
    margin-bottom: 2rem;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    border-radius: 4px;
    transition: width 0.5s ease;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    position: relative;
}

.step {
    text-align: center;
    flex: 1;
}

.step-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #e8f5e8;
    color: #999;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.75rem;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.step.completed .step-icon {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    color: white;
}

.step-label {
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
}

.step.completed .step-label {
    color: #2E7D32;
    font-weight: 600;
}

/* Section Cards */
.section-card {
    background: white;
    border: 2px solid #e8f5e8;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(46, 125, 50, 0.1);
}

.section-header {
    background: #f8f9fa;
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid #e8f5e8;
}

.section-header h4 {
    color: #2E7D32;
    margin-bottom: 0;
    font-size: 1.1rem;
}

.section-content {
    padding: 1.5rem;
}

/* Items List */
.items-list {
    margin-bottom: 1.5rem;
}

.item-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.item-row:last-child {
    border-bottom: none;
}

.item-info {
    flex: 1;
}

.item-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.25rem;
}

.item-category {
    font-size: 0.9rem;
    color: #666;
}

.item-quantity {
    margin: 0 1rem;
    text-align: center;
}

.quantity-label {
    display: block;
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 0.25rem;
}

.quantity-value {
    font-weight: 600;
    color: #2E7D32;
}

.item-price {
    text-align: right;
}

.unit-price {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.25rem;
}

.total-price {
    font-weight: 600;
    color: #2E7D32;
}

/* Order Summary */
.order-summary {
    border-top: 2px solid #e8f5e8;
    padding-top: 1rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.total-row {
    font-weight: 700;
    font-size: 1.1rem;
    color: #2E7D32;
    border-top: 1px solid #e8f5e8;
    padding-top: 0.75rem;
    margin-top: 0.75rem;
}

/* Timeline */
.timeline {
    position: relative;
}

.timeline-item {
    position: relative;
    padding-left: 2rem;
    margin-bottom: 1.5rem;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-marker {
    position: absolute;
    left: 0;
    top: 0.25rem;
    width: 12px;
    height: 12px;
    background: #4CAF50;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: 5px;
    top: 1rem;
    width: 2px;
    height: calc(100% + 0.5rem);
    background: #e8f5e8;
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.status-change {
    font-weight: 600;
    color: #2E7D32;
}

.timeline-date {
    font-size: 0.9rem;
    color: #666;
}

.timeline-notes {
    font-size: 0.9rem;
    color: #666;
    font-style: italic;
}

/* Info Rows */
.info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.info-row:last-child {
    margin-bottom: 0;
}

.info-label {
    font-weight: 600;
    color: #666;
}

.info-value {
    color: #333;
}

.address-display {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #e8f5e8;
}

/* Action Buttons */
.action-buttons .btn {
    margin-bottom: 0.5rem;
}

.action-buttons .btn:last-child {
    margin-bottom: 0;
}

@media (max-width: 768px) {
    .tracking-header .row {
        text-align: center;
    }
    
    .order-total-display {
        text-align: center;
        margin-top: 1rem;
    }
    
    .progress-steps {
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .step {
        flex: 0 0 calc(50% - 0.5rem);
    }
    
    .item-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .item-quantity,
    .item-price {
        margin: 0;
        text-align: left;
    }
}
</style>

<script>
function cancelOrder(orderId) {
    if (confirm('Are you sure you want to cancel this order?')) {
        // Add cancel order functionality here
        alert('Order cancellation feature will be implemented soon.');
    }
}
</script>
{% endblock %}
