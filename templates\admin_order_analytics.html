{% extends "base.html" %}

{% block title %}Order Analytics - Admin Dashboard{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-chart-bar"></i> Order Analytics</h2>
        <div>
            <a href="{{ url_for('admin_dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
            <a href="{{ url_for('admin_logout') }}" class="btn btn-danger ms-2">
                <i class="fas fa-sign-out-alt"></i> Logout
            </a>
        </div>
    </div>

    <!-- Key Metrics Row -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Orders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ analytics.total_orders }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Revenue</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">${{ "%.2f"|format(analytics.total_revenue) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Average Order Value</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">${{ "%.2f"|format(analytics.average_order_value) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calculator fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Delivered Orders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ analytics.delivered_orders }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Status Breakdown -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Order Status Breakdown</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span class="text-warning">Pending</span>
                                    <span class="font-weight-bold">{{ analytics.pending_orders }}</span>
                                </div>
                                <div class="progress mb-2">
                                    <div class="progress-bar bg-warning" style="width: {{ (analytics.pending_orders / analytics.total_orders * 100) if analytics.total_orders > 0 else 0 }}%"></div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span class="text-info">Confirmed</span>
                                    <span class="font-weight-bold">{{ analytics.confirmed_orders }}</span>
                                </div>
                                <div class="progress mb-2">
                                    <div class="progress-bar bg-info" style="width: {{ (analytics.confirmed_orders / analytics.total_orders * 100) if analytics.total_orders > 0 else 0 }}%"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span class="text-primary">Shipped</span>
                                    <span class="font-weight-bold">{{ analytics.shipped_orders }}</span>
                                </div>
                                <div class="progress mb-2">
                                    <div class="progress-bar bg-primary" style="width: {{ (analytics.shipped_orders / analytics.total_orders * 100) if analytics.total_orders > 0 else 0 }}%"></div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span class="text-success">Delivered</span>
                                    <span class="font-weight-bold">{{ analytics.delivered_orders }}</span>
                                </div>
                                <div class="progress mb-2">
                                    <div class="progress-bar bg-success" style="width: {{ (analytics.delivered_orders / analytics.total_orders * 100) if analytics.total_orders > 0 else 0 }}%"></div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span class="text-danger">Cancelled</span>
                                    <span class="font-weight-bold">{{ analytics.cancelled_orders }}</span>
                                </div>
                                <div class="progress mb-2">
                                    <div class="progress-bar bg-danger" style="width: {{ (analytics.cancelled_orders / analytics.total_orders * 100) if analytics.total_orders > 0 else 0 }}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Order Activity</h6>
                </div>
                <div class="card-body">
                    {% if analytics.recent_orders %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Order ID</th>
                                        <th>Date</th>
                                        <th>Total</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for order in analytics.recent_orders[:10] %}
                                    <tr>
                                        <td>
                                            <a href="{{ url_for('admin_order_detail', order_id=order.get('order_id')) }}" class="text-decoration-none">
                                                {{ order.get('order_id', 'N/A')[:8] }}...
                                            </a>
                                        </td>
                                        <td>{{ order.get('date', 'N/A')[:10] }}</td>
                                        <td>${{ "%.2f"|format(order.get('total', 0)) }}</td>
                                        <td>
                                            <span class="badge 
                                                {% if order.get('status') == 'pending' %}bg-warning text-dark
                                                {% elif order.get('status') == 'confirmed' %}bg-info
                                                {% elif order.get('status') == 'shipped' %}bg-primary
                                                {% elif order.get('status') == 'delivered' %}bg-success
                                                {% elif order.get('status') == 'cancelled' %}bg-danger
                                                {% else %}bg-secondary{% endif %}">
                                                {{ order.get('status', 'Unknown').title() }}
                                            </span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center text-muted">
                            <i class="fas fa-inbox fa-3x mb-3"></i>
                            <p>No recent orders found</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Insights -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Performance Insights</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center">
                                <h5 class="text-success">
                                    {{ "%.1f"|format((analytics.delivered_orders / analytics.total_orders * 100) if analytics.total_orders > 0 else 0) }}%
                                </h5>
                                <p class="text-muted">Order Completion Rate</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h5 class="text-info">
                                    {{ "%.1f"|format((analytics.pending_orders / analytics.total_orders * 100) if analytics.total_orders > 0 else 0) }}%
                                </h5>
                                <p class="text-muted">Orders Pending Processing</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h5 class="text-warning">
                                    {{ "%.1f"|format((analytics.cancelled_orders / analytics.total_orders * 100) if analytics.total_orders > 0 else 0) }}%
                                </h5>
                                <p class="text-muted">Cancellation Rate</p>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-12">
                            <h6 class="font-weight-bold mb-3">Quick Actions</h6>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('admin_dashboard') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-list"></i> View All Orders
                                </a>
                                <a href="{{ url_for('admin_inventory') }}" class="btn btn-outline-success">
                                    <i class="fas fa-boxes"></i> Manage Inventory
                                </a>
                                <button class="btn btn-outline-info" onclick="window.print()">
                                    <i class="fas fa-print"></i> Print Report
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
