{% extends "admin/base.html" %}
{% block title %}Inventory Management{% endblock %}

{% block extra_css %}
<style>
    .inventory-card {
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s ease;
    }
    
    .inventory-card:hover {
        transform: translateY(-2px);
    }
    
    .stat-icon {
        font-size: 2.5rem;
        margin-bottom: 10px;
    }
    
    .stock-level-low {
        color: #dc3545;
        font-weight: bold;
    }
    
    .stock-level-medium {
        color: #ffc107;
        font-weight: bold;
    }
    
    .stock-level-good {
        color: #28a745;
        font-weight: bold;
    }
    
    .alert-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background: #dc3545;
        color: white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        font-size: 0.7rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .transaction-item {
        border-left: 4px solid #dee2e6;
        padding: 10px 15px;
        margin-bottom: 10px;
        background: #f8f9fa;
        border-radius: 0 8px 8px 0;
    }
    
    .transaction-increase {
        border-left-color: #28a745;
    }
    
    .transaction-decrease {
        border-left-color: #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <h2><i class="fas fa-boxes"></i> Inventory Management Dashboard</h2>
            <p class="text-muted">Real-time inventory tracking and management</p>
        </div>
    </div>
    
    <!-- Inventory Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card inventory-card bg-primary text-white">
                <div class="card-body text-center">
                    <i class="fas fa-cubes stat-icon"></i>
                    <h3>{{ total_products }}</h3>
                    <p class="mb-0">Total Products</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card inventory-card bg-warning text-white position-relative">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation-triangle stat-icon"></i>
                    <h3>{{ low_stock_count }}</h3>
                    <p class="mb-0">Low Stock Items</p>
                    {% if low_stock_count > 0 %}
                    <span class="alert-badge">!</span>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card inventory-card bg-danger text-white position-relative">
                <div class="card-body text-center">
                    <i class="fas fa-times-circle stat-icon"></i>
                    <h3>{{ out_of_stock_count }}</h3>
                    <p class="mb-0">Out of Stock</p>
                    {% if out_of_stock_count > 0 %}
                    <span class="alert-badge">!</span>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card inventory-card bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-dollar-sign stat-icon"></i>
                    <h3>${{ "%.2f"|format(total_value) }}</h3>
                    <p class="mb-0">Total Inventory Value</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-bolt"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="btn-group" role="group">
                        <a href="{{ url_for('admin_low_stock_alerts') }}" class="btn btn-warning">
                            <i class="fas fa-exclamation-triangle"></i> View Low Stock Alerts
                            {% if active_alerts|length > 0 %}
                            <span class="badge bg-danger ms-1">{{ active_alerts|length }}</span>
                            {% endif %}
                        </a>
                        <a href="{{ url_for('admin_inventory_logs') }}" class="btn btn-info">
                            <i class="fas fa-history"></i> View Transaction Logs
                        </a>
                        <a href="{{ url_for('admin_products') }}" class="btn btn-primary">
                            <i class="fas fa-edit"></i> Manage Products
                        </a>
                        <button class="btn btn-success" onclick="refreshInventory()">
                            <i class="fas fa-sync-alt"></i> Refresh Data
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Products by Stock Level -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> Products by Stock Level</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Current Stock</th>
                                    <th>Threshold</th>
                                    <th>Status</th>
                                    <th>Value</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in products_by_stock %}
                                <tr>
                                    <td>
                                        <strong>{{ product.name }}</strong><br>
                                        <small class="text-muted">{{ product.category.name if product.category else 'No Category' }}</small>
                                    </td>
                                    <td>
                                        <span class="{% if product.stock_quantity == 0 %}stock-level-low{% elif product.stock_quantity <= product.low_stock_threshold %}stock-level-medium{% else %}stock-level-good{% endif %}">
                                            {{ product.stock_quantity }} {{ product.unit }}
                                        </span>
                                    </td>
                                    <td>{{ product.low_stock_threshold }}</td>
                                    <td>
                                        {% if product.stock_quantity == 0 %}
                                            <span class="badge bg-danger">Out of Stock</span>
                                        {% elif product.stock_quantity <= product.low_stock_threshold %}
                                            <span class="badge bg-warning">Low Stock</span>
                                        {% else %}
                                            <span class="badge bg-success">In Stock</span>
                                        {% endif %}
                                    </td>
                                    <td>${{ "%.2f"|format(product.stock_quantity * product.price) }}</td>
                                    <td>
                                        <a href="{{ url_for('admin_edit_product', product_id=product.id) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Recent Transactions & Alerts -->
        <div class="col-md-4">
            <!-- Active Alerts -->
            {% if active_alerts %}
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h6><i class="fas fa-exclamation-triangle"></i> Active Low Stock Alerts</h6>
                </div>
                <div class="card-body">
                    {% for alert in active_alerts[:5] %}
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        <strong>{{ alert.product_name }}</strong><br>
                        <small>{{ alert.current_stock }} units remaining (threshold: {{ alert.threshold }})</small>
                        <button type="button" class="btn-close" onclick="resolveAlert('{{ alert.id }}')" aria-label="Close"></button>
                    </div>
                    {% endfor %}
                    
                    {% if active_alerts|length > 5 %}
                    <a href="{{ url_for('admin_low_stock_alerts') }}" class="btn btn-sm btn-warning">
                        View All {{ active_alerts|length }} Alerts
                    </a>
                    {% endif %}
                </div>
            </div>
            {% endif %}
            
            <!-- Recent Transactions -->
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-history"></i> Recent Inventory Changes</h6>
                </div>
                <div class="card-body">
                    {% if recent_transactions %}
                        {% for transaction in recent_transactions %}
                        <div class="transaction-item {% if transaction.quantity_change > 0 %}transaction-increase{% else %}transaction-decrease{% endif %}">
                            <div class="d-flex justify-content-between">
                                <strong>{{ transaction.product.name if transaction.product else 'Unknown Product' }}</strong>
                                <span class="{% if transaction.quantity_change > 0 %}text-success{% else %}text-danger{% endif %}">
                                    {{ "+" if transaction.quantity_change > 0 else "" }}{{ transaction.quantity_change }}
                                </span>
                            </div>
                            <small class="text-muted">
                                {{ transaction.transaction_type.title() }} - {{ transaction.created_at.strftime('%Y-%m-%d %H:%M') }}
                            </small>
                            {% if transaction.notes %}
                            <div><small>{{ transaction.notes }}</small></div>
                            {% endif %}
                        </div>
                        {% endfor %}
                        
                        <a href="{{ url_for('admin_inventory_logs') }}" class="btn btn-sm btn-outline-info">
                            View All Transactions
                        </a>
                    {% else %}
                        <p class="text-muted">No recent transactions</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function refreshInventory() {
    location.reload();
}

function resolveAlert(alertId) {
    if (confirm('Mark this alert as resolved?')) {
        fetch(`/admin/resolve-alert/${alertId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Error resolving alert');
            }
        })
        .catch(error => {
            alert('Error resolving alert');
        });
    }
}

// Auto-refresh every 5 minutes
setInterval(function() {
    location.reload();
}, 300000);
</script>
{% endblock %}
