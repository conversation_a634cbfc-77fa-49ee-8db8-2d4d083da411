#!/usr/bin/env python3

import os
import sys
import json

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from models import User

def debug_order_tracking():
    with app.app_context():
        print("=== DEBUGGING ORDER TRACKING SYSTEM ===\n")
        
        # Check users
        users = User.query.all()
        print(f"📊 DATABASE USERS ({len(users)}):")
        for user in users:
            print(f"   • {user.email} - {user.first_name} {user.last_name}")
        
        # Check orders
        try:
            with open('orders.json', 'r') as f:
                orders = json.load(f)
                print(f"\n📦 ORDERS.JSON ({len(orders)}):")
                for order in orders:
                    status = order.get('status', 'No status')
                    print(f"   • Order {order.get('order_id')}: {order.get('email')} - Status: {status}")
                    if order.get('items'):
                        print(f"     Items: {len(order.get('items'))} items, Total: ${order.get('total', 0)}")
        except Exception as e:
            print(f"❌ Error reading orders.json: {e}")
            return
        
        # Test email matching
        print(f"\n🔍 EMAIL MATCHING TEST:")
        test_email = '<EMAIL>'
        user = User.query.filter_by(email=test_email).first()
        if user:
            print(f"   ✅ User found: {user.email}")
            
            # Test order filtering
            matching_orders = []
            for order in orders:
                if order.get('email') == test_email:
                    matching_orders.append(order)
            
            print(f"   📋 Matching orders: {len(matching_orders)}")
            for order in matching_orders:
                print(f"      • {order.get('order_id')} - {order.get('status', 'No status')}")
        else:
            print(f"   ❌ No user found with email: {test_email}")
        
        # Test load_orders function
        print(f"\n🔧 TESTING load_orders() FUNCTION:")
        try:
            from app import load_orders
            loaded_orders = load_orders()
            print(f"   ✅ load_orders() returned {len(loaded_orders)} orders")
        except Exception as e:
            print(f"   ❌ Error with load_orders(): {e}")

if __name__ == "__main__":
    debug_order_tracking()
