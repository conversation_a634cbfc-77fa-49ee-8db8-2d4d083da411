#!/usr/bin/env python3

"""
Minimal Flask app to test cart session functionality
"""

from flask import Flask, session, request, redirect, url_for, render_template_string
import json

app = Flask(__name__)
app.secret_key = 'test_cart_session_key'

# Sample products data
PRODUCTS = {
    'Organic Bananas': {'price': 2.49, 'stock': 30},
    'Apple': {'price': 0.99, 'stock': 50},
    'Milk': {'price': 3.99, 'stock': 20},
    'Bread': {'price': 2.50, 'stock': 15}
}

# Templates
HOME_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>Cart Test App</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .product { border: 1px solid #ddd; padding: 10px; margin: 10px 0; }
        .cart-item { background: #f9f9f9; padding: 5px; margin: 5px 0; }
        button { padding: 5px 10px; margin: 5px; }
        .total { font-weight: bold; font-size: 1.2em; color: green; }
    </style>
</head>
<body>
    <h1>🛒 Cart Test Application</h1>
    
    <h2>Products</h2>
    {% for name, details in products.items() %}
    <div class="product">
        <strong>{{ name }}</strong> - ${{ "%.2f"|format(details.price) }} 
        (Stock: {{ details.stock }})
        <form action="/add_to_cart/{{ name }}" method="post" style="display: inline;">
            <input type="number" name="quantity" value="1" min="1" max="{{ details.stock }}" style="width: 60px;">
            <button type="submit">Add to Cart</button>
        </form>
    </div>
    {% endfor %}
    
    <h2>Current Cart</h2>
    {% if cart and cart|length > 0 %}
        {% for item in cart %}
        <div class="cart-item">
            {{ item.name }} - Qty: {{ item.quantity }} - 
            ${{ "%.2f"|format(item.price) }} each = 
            ${{ "%.2f"|format(item.price * item.quantity) }}
            <a href="/remove_from_cart/{{ item.name }}">Remove</a>
        </div>
        {% endfor %}
        <div class="total">Total: ${{ "%.2f"|format(total) }}</div>
        <button onclick="location.href='/clear_cart'">Clear Cart</button>
    {% else %}
        <p>Your cart is empty</p>
    {% endif %}
    
    <h2>Debug Info</h2>
    <p>Session ID: {{ session.get('_id', 'No ID') }}</p>
    <p>Cart exists in session: {{ 'cart' in session }}</p>
    <p>Cart length: {{ cart|length if cart else 0 }}</p>
    <p>Session keys: {{ session.keys()|list }}</p>
    
    <hr>
    <a href="/debug">View Raw Debug Data</a>
</body>
</html>
"""

@app.route('/')
def home():
    """Home page with products and cart"""
    cart = session.get('cart', [])
    total = sum(item.get('price', 0) * item.get('quantity', 0) for item in cart)
    
    return render_template_string(HOME_TEMPLATE, 
                                products=PRODUCTS, 
                                cart=cart, 
                                total=total,
                                session=session)

@app.route('/add_to_cart/<item_name>', methods=['POST'])
def add_to_cart(item_name):
    """Add item to cart"""
    try:
        quantity = int(request.form.get('quantity', 1))
        
        if item_name not in PRODUCTS:
            return f"Product {item_name} not found!", 404
        
        product = PRODUCTS[item_name]
        
        # Get current cart
        cart = session.get('cart', [])
        
        # Check if item already in cart
        existing_item = next((item for item in cart if item['name'] == item_name), None)
        
        if existing_item:
            existing_item['quantity'] += quantity
        else:
            cart.append({
                'name': item_name,
                'price': product['price'],
                'quantity': quantity
            })
        
        # Save cart to session
        session['cart'] = cart
        session.permanent = True  # Make session persistent
        
        print(f"✅ Added {quantity} {item_name} to cart. Cart now has {len(cart)} items")
        
        return redirect(url_for('home'))
        
    except Exception as e:
        print(f"❌ Error adding to cart: {e}")
        return f"Error: {e}", 500

@app.route('/remove_from_cart/<item_name>')
def remove_from_cart(item_name):
    """Remove item from cart"""
    cart = session.get('cart', [])
    cart = [item for item in cart if item['name'] != item_name]
    session['cart'] = cart
    session.permanent = True
    
    print(f"🗑️ Removed {item_name} from cart")
    return redirect(url_for('home'))

@app.route('/clear_cart')
def clear_cart():
    """Clear entire cart"""
    session['cart'] = []
    session.permanent = True
    
    print("🧹 Cart cleared")
    return redirect(url_for('home'))

@app.route('/debug')
def debug():
    """Debug route showing raw session data"""
    cart = session.get('cart', [])
    
    debug_data = {
        'session_data': dict(session),
        'cart': cart,
        'cart_length': len(cart),
        'cart_exists': 'cart' in session,
        'session_permanent': session.permanent,
        'total': sum(item.get('price', 0) * item.get('quantity', 0) for item in cart)
    }
    
    return f"<h1>🔍 Debug Data</h1><pre>{json.dumps(debug_data, indent=2)}</pre><a href='/'>Back to Home</a>"

if __name__ == '__main__':
    print("🚀 Starting minimal cart test app...")
    print("Visit http://127.0.0.1:5002 to test cart functionality")
    app.run(debug=True, host='127.0.0.1', port=5002)
